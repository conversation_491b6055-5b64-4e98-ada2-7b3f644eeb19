#!/bin/bash

# Quick restart script for server services
# Usage: ./quick_restart.sh [uwsgi|celery|both]

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
SERVER_HOST="micado.be"
BACKEND_DIR="/var/www/micado-web-api"
SERVICE=${1:-"both"}

print_status() {
    local service=$1
    local status=$2
    local timestamp=$(date '+%H:%M:%S')
    
    if [ "$status" = "OK" ]; then
        echo -e "[$timestamp] ${GREEN}✓${NC} $service: ${GREEN}$status${NC}"
    elif [ "$status" = "FAIL" ]; then
        echo -e "[$timestamp] ${RED}✗${NC} $service: ${RED}$status${NC}"
    else
        echo -e "[$timestamp] ${BLUE}ℹ${NC} $service: ${BLUE}$status${NC}"
    fi
}

restart_uwsgi() {
    echo -e "${BLUE}=== Restarting uWSGI ===${NC}"
    
    # Kill uWSGI processes
    print_status "uWSGI" "Stopping..."
    ssh "$SERVER_HOST" "pkill -f uwsgi || true" 2>/dev/null
    sleep 2
    
    # Start uWSGI
    print_status "uWSGI" "Starting..."
    ssh "$SERVER_HOST" "cd $BACKEND_DIR && uwsgi --ini uwsgi.ini --daemonize uwsgi.log" 2>/dev/null
    sleep 3
    
    # Check status
    if ssh "$SERVER_HOST" "ps aux | grep -v grep | grep uwsgi" >/dev/null 2>&1; then
        print_status "uWSGI" "OK"
    else
        print_status "uWSGI" "FAIL"
    fi
}

restart_celery() {
    echo -e "${BLUE}=== Restarting Celery ===${NC}"

    # Kill Celery processes (both user and root processes)
    print_status "Celery" "Stopping..."
    ssh "$SERVER_HOST" "pkill -f 'celery.*worker' || true" 2>/dev/null
    ssh "$SERVER_HOST" "sudo pkill -f 'celery.*worker' || true" 2>/dev/null
    sleep 2

    # Force kill if needed
    ssh "$SERVER_HOST" "pkill -9 -f 'celery.*worker' || true" 2>/dev/null
    ssh "$SERVER_HOST" "sudo pkill -9 -f 'celery.*worker' || true" 2>/dev/null
    sleep 1

    # Start Celery as ubuntu user
    print_status "Celery" "Starting..."
    ssh "$SERVER_HOST" "cd $BACKEND_DIR && nohup venv/bin/celery -A main worker --loglevel=info > celery.log 2>&1 &" 2>/dev/null
    sleep 3

    # Check status
    local celery_count=$(ssh "$SERVER_HOST" "ps aux | grep -v grep | grep 'celery.*worker' | wc -l" 2>/dev/null)
    if [ "$celery_count" -gt 0 ]; then
        print_status "Celery" "OK ($celery_count processes)"
    else
        print_status "Celery" "FAIL"
    fi
}

check_api() {
    echo -e "${BLUE}=== Checking API ===${NC}"
    
    local api_response=$(curl -s -o /dev/null -w "%{http_code}" "https://api.levitt.ai/api/" 2>/dev/null || echo "000")
    if [ "$api_response" = "200" ]; then
        print_status "API" "OK (HTTP $api_response)"
    else
        print_status "API" "FAIL (HTTP $api_response)"
    fi
}

# Main execution
case "$SERVICE" in
    "uwsgi")
        restart_uwsgi
        ;;
    "celery")
        restart_celery
        ;;
    "both")
        restart_uwsgi
        restart_celery
        ;;
    *)
        echo -e "${RED}Invalid service. Use: uwsgi, celery, or both${NC}"
        echo "Usage: $0 [uwsgi|celery|both]"
        exit 1
        ;;
esac

check_api

echo ""
echo -e "${GREEN}=== Restart completed ===${NC}"
