#!/bin/bash

# Simple restart script without hanging
# Usage: ./simple_restart.sh

echo "=== Simple Server Restart ==="

# Kill processes with timeout
echo "1. Stopping services..."
timeout 10 ssh micado.be "pkill -f uwsgi; pkill -f 'celery.*worker'" || echo "Some processes may still be running"

echo "2. Waiting..."
sleep 3

# Start services
echo "3. Starting uWSGI..."
ssh micado.be "cd /var/www/micado-web-api && uwsgi --ini uwsgi.ini --daemonize uwsgi.log" &

echo "4. Starting Celery..."
ssh micado.be "cd /var/www/micado-web-api && nohup venv/bin/celery -A main worker --loglevel=info > celery.log 2>&1 &" &

echo "5. Waiting for services to start..."
sleep 5

# Check status
echo "6. Checking status..."
uwsgi_count=$(ssh micado.be "ps aux | grep -v grep | grep uwsgi | wc -l" 2>/dev/null || echo "0")
celery_count=$(ssh micado.be "ps aux | grep -v grep | grep 'celery.*worker' | wc -l" 2>/dev/null || echo "0")

echo "   uWSGI processes: $uwsgi_count"
echo "   Celery processes: $celery_count"

# Test API
echo "7. Testing API..."
api_status=$(curl -s -o /dev/null -w "%{http_code}" "https://api.levitt.ai/api/" 2>/dev/null || echo "000")
echo "   API status: HTTP $api_status"

echo "=== Restart completed ==="
