{"name": "micado-web-fe", "version": "0.1.0", "private": true, "dependencies": {"@ckeditor/ckeditor5-build-classic": "^40.0.0", "@ckeditor/ckeditor5-react": "^6.1.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.3", "@mui/material": "^5.14.4", "@mui/x-date-pickers": "^6.11.1", "@react-pdf/renderer": "^3.1.14", "@reduxjs/toolkit": "^1.9.5", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.5.1", "buffer": "^6.0.3", "date-fns": "^2.30.0", "dayjs": "^1.11.10", "formik": "^2.4.5", "html2canvas": "^1.4.1", "iconv-lite": "^0.6.3", "moment": "^2.29.4", "papaparse": "^5.4.1", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-csv": "^2.2.2", "react-datepicker": "^4.21.0", "react-dom": "^18.2.0", "react-drag-drop-files": "^2.3.10", "react-helmet-async": "^2.0.3", "react-pdf": "^7.5.1", "react-redux": "^8.1.2", "react-router-dom": "^5.3.4", "react-scripts": "5.0.1", "react-toastify": "^9.1.3", "recharts": "^2.7.3", "sass": "^1.65.1", "web-vitals": "^2.1.4", "yup": "^1.3.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}