.contact {
  &--title {
    font-weight: bold !important;
    color: #2E4E76;
    display: flex;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid #97CBE2;
  }

  &-card {
    border-radius: 10px !important;
    box-shadow: 0px 3px 6px #2C28281C !important;

    &--header {
      display: flex;
      padding: 22px 28px;
      background: #F7F7F8;
      color: #2E4E76;
      align-items: center;
    }

    &--title {
      font-size: 16px !important;
      font-weight: bold !important;
      color: #2E4E76 !important;
    }

    &--subtitle {
      font-size: 14px !important;
      font-weight: 500 !important;
      color: #2E4E76 !important;
    }

    &--body {
      display: grid;
      grid-template-columns: 30% 70%;
      padding: 32px 30px;
    }

    &--text {
      color: #2E4E76 !important;
      font-weight: 500 !important;
      font-size: 16px !important;
    }

    &--input {
      input {
        padding: 12px 24px;
        border-radius: 8px;
        color: #2E4E76;
        font-weight: 500;
      }

      fieldset {
        border-color: #B7E1F1 !important;
        border-radius: 8px;
      }
    }

    &--button {
      padding: 12px 28px !important;
      background-color: #7764E4 !important;
      color: #FFFFFF !important;
      font-weight: bold !important;
      font-size: 13px !important;
      border-radius: 5px !important;

      &:hover {
        background-color: #5f48e1 !important;
      }
    }
  }
}