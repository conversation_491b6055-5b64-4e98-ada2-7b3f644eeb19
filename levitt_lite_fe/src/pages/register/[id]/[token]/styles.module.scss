$main_color: #2E4E76;
$footer_height: 60px;
$header_height: 70px;

.content-account {
  text-align: center;
  font: bold 24px/24px var(--font-family),
  sans-serif !important;
  color: #1a1b35;
}

.content-account {
  display: flex;
  width: 100%;
  justify-content: center;
}

.content-bottom-account {
  margin-top: 25px !important;
  text-align: center;
  font: 500 14px/24px var(--font-family);
  letter-spacing: 0px;
  color: $main_color;
}

.box-main-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.box-text-link {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  .box-text {
    width: 30%;
    text-align: end;
    display: flex;
    justify-content: flex-end;
    margin-top: 8px;
    .text-link {
      font: 500 14px/18px var(--font-family);
      letter-spacing: 0px;
      color: #3F7EAE;
    }
  }
}

.custom-field {
  width: 30%;
  margin-top: 30px !important;
  font: bold 16px/18px var(--font-family);
  font-weight: bold !important;
}

.content-bot-account {
  text-align: center;
  font: bold 24px/24px var(--font-family),
  sans-serif;
  color: #1a1b35;
  margin-top: 25px !important;
}

.box-continue {
  display: flex;
  justify-content: center;
  margin-top: 28px;
  border-radius: 5px;

  .text-button {
    border: 1px solid $main_color;
    padding: 15px 25px 15px 25px;
    font: bold 16px/20px var(--font-family);
    color: #ffff;
    width: 30%;
    text-align: center;
    background: $main_color 0% 0% no-repeat padding-box;
    border-radius: 5px;
    cursor: pointer;
    &:hover {
      background-color: $main_color ;
    }
  }
}

.box-content {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 100%;
}
