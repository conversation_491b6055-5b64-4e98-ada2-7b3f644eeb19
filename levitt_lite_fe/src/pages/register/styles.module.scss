$main_color: #2E4E76;
$footer_height: 60px;
$header_height: 70px;

.box-content {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 100%;
  padding: 40px 0;

  .error-text {
    color: #CE858F;
    font: 500 14px/18px var(--font-family);
  }
}

.content-email {
  text-align: center;
  font: bold 24px/24px var(--font-family),
  sans-serif !important;
  color: #1a1b35;
}

.content-email {
  display: flex;
  width: 100%;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  font: bold 24px/24px var(--font-family) !important;
}

.content-bottom-email {
  margin-top: 8px !important;
  text-align: center;
  color: #1a1b35;
  width: 25%;
  font: 500 12px/18px var(--font-family) !important;
}

.link {
  color: #3f7eae;
  font: bold 14px/18px var(--font-family);
}

.box-continue {
  display: flex;
  justify-content: center;
  margin-top: 14px;
  border-radius: 5px;

  .text-button {
    border: 1px solid $main_color;
    opacity: 1;
    padding: 16px 0;
    font: bold 16px/20px var(--font-family),
    sans-serif;
    color: #ffff;
    width: 30%;
    text-align: center;
    background: $main_color 0% 0% no-repeat padding-box;
    border-radius: 5px;
    cursor: pointer;
  }
}

.custom-field {
  width: 30%;
  font: bold 16px/18px var(--font-family);
  margin-top: 30px !important;
}

.more-action-information {
  text-align: center;
  margin-top: 20px;
  color: #1A1B35;
  font: bold 14px/18px var(--font-family);
}

.box-message {
  display: flex;
}

.error-message {
  color: #CE858F;
  font: bold 14px/18px var(--font-family);
  margin-left: 10px;
}

.custom-error-message {
  color: #d32f2f;
  font-size: 0.75rem;
  font-weight: 400;
  line-height: 1.66;
  padding-top: 10px;
}
