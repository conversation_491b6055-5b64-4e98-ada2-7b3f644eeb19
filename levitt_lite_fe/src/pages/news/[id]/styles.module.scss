$main_color: #2E4E76;
$color_white: #FFFFFF;
$title-color: #3F7EAE;
$footer_height: 140px;
$header_height: 70px;

.box-main-content {
  width: 100%;
  flex-grow: 1;
  background: #F1F3F9;

  .box-content {
    display: flex;
    justify-content: space-between;
    border-top: 1px solid $title-color;
    border-bottom: 1px solid $title-color;
    padding: 20px 0;
    margin: 0 80px;
  }
}

.box-header {
  padding-left: 80px;
  padding-right: 80px;
  padding-top: 120px;

  .title-header {
    font: normal normal bold 32px/30px 'Montserrat', sans-serif;
    letter-spacing: 1px;
    color: $main_color;
    text-transform: uppercase;
    margin-bottom: 40px;
    text-align: center;
    line-height: 1.25;
  }
}

.pagination-footer {
  display: flex;
  gap: 36px;
  justify-content: center;

  .pagination-title {
    font: 500 20px/30px var(--font-family);
    letter-spacing: 2px;
    color: $main_color;
    margin-top: 40px;
    cursor: pointer;
  }
}

.box-content {
  .box {
    display: flex;
    gap: 35px;
    width: 100%;

    .text {
      font: bold 16px/30px var(--font-family);
      letter-spacing: 1.6px;
      color: $main_color;
    }

    .title {
      font: bold 24px/30px var(--font-family);
      letter-spacing: 1.6px;
      color: $main_color;
      padding-right: 25px;
    }
  }
}

.text-link {
  font: normal normal bold 20px/30px 'Montserrat', sans-serif;
  letter-spacing: 1.6px;
  color: $title-color;
  text-align: right;
  text-decoration: none;
}
