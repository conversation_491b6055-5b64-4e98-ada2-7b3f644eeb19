.text-title {
  text-align: left;
  letter-spacing: 0px;
  color: #2e4e76;
  opacity: 1;
  border-bottom: 2px solid #97cbe2;
  height: 52px;
  font-size: 20px !important;
  font-weight: bold !important;
}

.box-button-edit {
  margin-top: 24px;
  width: 100%;
  height: 90%;
  margin-right: 80px;
  padding: 30px;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000052;
  border-radius: 10px;
  opacity: 1;
  cursor: pointer;

  .icon-edit {
    height: 100px;
    width: 100%;
    color: #2e4e76;
  }

  .button-edit {
    background: #e6e6ef 0% 0% no-repeat padding-box;
    box-shadow: 0px 0px 8px #00000029;
    border-radius: 4px;
    opacity: 1;
    width: 50%;
    letter-spacing: 0px;
    color: #3f7eae;
    text-align: center;
    margin-left: 82px;
  }

  .text-button-edit {
    letter-spacing: 0px;
    font-size: 20px !important;
    color: #2e4e76;
    opacity: 1;
    font-weight: bold;
    display: flex;
    align-content: stretch;
    flex-direction: column-reverse;
    justify-content: space-around;
    text-align: center;
  }
}

.box-content-right {
  margin-top: 44px;
  padding: 11px;
  opacity: 1;
  background: #7764E4 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #2C28281C;
  border: 1px solid #7764E4;
  border-radius: 5px;

  .text-content-right {
    letter-spacing: 0px;
    font-size: 12px !important;
    color: #ffff;
    opacity: 1;
    font-weight: bold;
    display: flex;
    align-content: stretch;
    flex-direction: column-reverse;
    justify-content: space-around;
    text-align: center;
  }

  .icon-modal {
    color: #CE858F;
    font-size: 110px;
  }
}
