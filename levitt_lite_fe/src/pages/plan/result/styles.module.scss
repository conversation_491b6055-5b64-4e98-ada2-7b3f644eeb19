.text-tab {
  box-shadow: inset 0px 3px 6px #00000029;
  border-radius: 10px 0px 0px 10px !important;
  background-color: #fff !important;
  margin-top: 24px !important;
  opacity: 1 !important;
  min-width: 60px !important;
  max-width: 60px !important;
  color: #2E4E76 !important;
}

.tab-panel {
  padding: 40px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0px 3px 6px #2C28281C;
}

.action-tab {
  background-color: #BBBBC7 !important;
  border-radius: 10px 0px 0px 10px !important;
  color: #fff !important;
  min-width: 60px !important;
  max-width: 60px !important;
}

.text-title {
  text-align: left;
  letter-spacing: 0px;
  color: #2E4E76;
  opacity: 1;
  border-bottom: 1px solid #97CBE2;
  opacity: 1;
  font-size: 20px !important;
  font-weight: bold !important;
  padding-bottom: 16px;
  margin-bottom: 20px !important;
}

.text-area {
  margin-top: 10px;
  border: 1px solid #97CBE2 !important;
  border-radius: 10px;
  max-width: 100%;
}

.text-input {
  width: 100%;

  fieldset {
    border-color:#97CBE2 !important;
    border-radius: 10px;
  }
input,
textarea {
  padding: 20px;
  font-size: 16px;
  font-weight: bold;
  color: #2E4E76;
}
}

.text-number {
  text-align: right;
  font: normal normal bold 16px/29px var(--font-family);
  letter-spacing: 0px;
  color: #97CBE2;
  opacity: 1;
}

.text-inputs {
  width: 50%;
  float: left;

  fieldset {
    border: 1px solid #97CBE2 !important;
    border-radius: 10px;
  }
}

.text-box {
  color: #2E4E76;
  font-size: 20px !important;
  font-weight: bold !important;
  white-space: pre-line !important;
}

.text-select {
  fieldset {
    border: 1px solid #97CBE2 !important;
    border-radius: 10px;
  }
}

.text-title-box {
  background: #E6E6EF;
  border-radius: 10px;
  opacity: 1;
  padding: 10px;
  padding: 20px;
  font: 700 16px/28px var(--font-family);
  color: #2E4E76;
}

.box-button {
  background: #7764E4 !important;
  box-shadow: 0px 3px 6px #2C28281C;
  border-radius: 5px !important;
  padding: 16px 28px !important;
  text-decoration: none;

  &:hover {
    background: #5945cb !important;
  }

  .text-button {
    letter-spacing: 0px;
    color: #FFFFFF;
    opacity: 1;
    font-weight: bold;
    display: flex;
    align-content: stretch;
    flex-direction: column-reverse;
    justify-content: space-around;
    height: 100%;
    text-align: center;
  }
}

.box-button-sale {
  background: #97CBE2 !important;
  box-shadow: 0px 3px 6px #2C28281C;
  border-radius: 5px !important;
  padding: 16px 28px !important;

  &:hover {
    background: #5c92a9 !important;
  }


  .text-button-sale {
      letter-spacing: 0px;
      color: #FFFFFF;
      opacity: 1;
      font-weight: bold;
      display: flex;
      align-content: stretch;
      flex-direction: column-reverse;
      justify-content: space-around;
      height: 100%;
      text-align: center;
  }
}

.box-date {
  margin-left: 18px;
  width: 177px;
  height: 34px;
  background: #F7F7F8 0% 0% no-repeat padding-box;
  border: 0px solid #ced4da;
  font: normal normal 500 16px/13px var(--font-family);
  color: #2E4E76;
  padding: 13px;
}

.box-button-edit {
  margin-top: 24px;
  width: 38%;
  height: 100%;
  background: #BBBBC7 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #2C28281C;
  border-radius: 5px;
  opacity: 1;
  margin-right: 80px;
  padding: 30px;

  .icon-edit {
    height: 100px;
    width: 100%;
    color: #E6E6EF;
  }

  .button-edit {
    background: #E6E6EF 0% 0% no-repeat padding-box;
    box-shadow: 0px 0px 8px #00000029;
    border-radius: 4px;
    opacity: 1;
    width: 50%;
    letter-spacing: 0px;
    color: #3F7EAE;
    text-align: center;
    opacity: 1;
  }

  .text-button-edit {
      letter-spacing: 0px;
      color: #FFFFFF;
      opacity: 1;
      font-weight: bold;
      display: flex;
      align-content: stretch;
      flex-direction: column-reverse;
      justify-content: space-around;
      height: 100%;
      text-align: center;
  }
}

.box-button-cache {
  margin-top: 24px;
  width: 38%;
  height: 100%;
  background: #97CBE2 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #2C28281C;
  border-radius: 5px;
  opacity: 1;
  padding: 30px;

  .icon-cache {
    height: 100px;
    width: 100%;
    color: #E6E6EF;
  }

  .button-cache {
    background: #E6E6EF 0% 0% no-repeat padding-box;
    box-shadow: 0px 0px 8px #00000029;
    border-radius: 4px;
    opacity: 1;
    width: 50%;
    letter-spacing: 0px;
    color: #3F7EAE;
    text-align: center;
    opacity: 1;
  }

  .text-button-cache {
      letter-spacing: 0px;
      color: #FFFFFF;
      opacity: 1;
      font-weight: bold;
      display: flex;
      align-content: stretch;
      flex-direction: column-reverse;
      justify-content: space-around;
      height: 100%;
      text-align: center;
  }
}

.content-left {
  text-align: left;
  font: normal normal bold 20px/22px Gotham;
  letter-spacing: 0px;
  color: #1A1B35;
  opacity: 1;
  font-weight: bold !important;
  font-size: 20px !important;
  margin-right: 20px !important;
}

.content-center {
  letter-spacing: 0px;
  color: #1A1B35;
  opacity: 1;
  font-weight: medium !important;
  font-size: 13px !important;
  display: flex;
  align-items: center;
  margin-right: 10px !important;
}

.text-delete {
  width: 37%;
  text-align: center;
  letter-spacing: 0px;
  color: #F53C56;
  opacity: 1;
  font-size: 16px;
  font-weight: bold;
}

.box-delete {
  width: 15%;
  letter-spacing: 0px;
  color: #ffff;
  text-align: center;
  opacity: 1;
  background: #F53C56 0% 0% no-repeat padding-box;
  box-shadow: 0px 0px 8px #00000029;
  border-radius: 4px;
  font-weight: bold !important;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.plan-group-header {
  color: #2E4E76;

  .box-date {
    border: 1px solid #97CBE2;
    border-radius: 4px;
  }

  .plan-group-title {
    font-size: 18px;
    font-weight: bold;
  }

  &--text {
    font-size: 14px;
    font-weight: 500;
  }

  &--selected-rows {
    background-color: #2E4E76;
    width: 100%;
    padding: 16px 0;
    color: #fff;

    .deselected-rows {
      margin-right: 30px;
      cursor: pointer;

      &--text {
        font-size: 12px;
        font-weight: bold;
      }
    }
  }
}

.header--text {
  font-size: 14px !important;
  font-weight: 500 !important;
  padding: 10px 20px;

  &:first-child {
    border-right: 1px solid currentColor;
    margin-right: 20px;
  }
}

.group-buttons {
  color: #2E4E76;

  .button-text {
    cursor: pointer;
    text-align: center;
    min-width: 84px;
    padding: 12px 18px;
    background: transparent;
    border: none;
    color: #2E4E76;

    &.active {
      color: #7764E4;
    }

    &:hover {
      background-color: #E6E6EF;
    }

    p {
      font-size: 12px;
      font-weight: bold;
    }
  }
}

.table-footer-row {
  height: 80px;
  td {
    background-color: #E6E6EF;
    font-weight: bold;
    color: #2E4E76;
  }

  &.small-row {
    height: 40px;

    td {
      padding: 8px 16px;
    }
  }
}

.small-padding-table {
  tr {
    height: 40px !important;
  }

  td {
    padding: 8px 16px !important;
  }
}

.dropdown-menu {
  z-index: 100;
  background-color: #FFFFFF;
  border-radius: 4px;
  box-shadow: 0px 0px 8px #00000052;
}

.text-field {
  color: #2E4E76;
  margin: 2px 2px 12px 2px !important;
  border-color: #7764E4;

  div {
    color: currentColor;
  }
  input {
    padding: 16px;
    font-size: 12px;
  }

  fieldset {
    border-color: #7764E4 !important;
  }
}

.plan-group-import-container {
  display: flex;
  flex-direction: column;

  &--title {
    font-weight: bold;
    color: #2E4E76;
    display: flex;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid #97CBE2;
  }

  .plan-group-import--content {
    cursor: pointer;
    border: 2px dashed #BBBBC7;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;

    .upload-description {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;

      &--img {
        width: 100px;
        height: 100px;
        background-color: #e6e6ef;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #8f8f9f;
      }

      &--text {
        font-size: 20px;
        color: #8f8f9f;
        font-weight: bold;
        margin-top: 20px;
      }

      &--impotent {
        color: #F53C56;
        font-weight: 500;
        margin-top: 20px;
        line-height: 1.2;
      }
    }
  }
}

.plan-group-table {
  td {
    color: #2E4E76;
    font-size: 12px;
    font-weight: 500;
    border: none;

    &:last-child {
      color: #def2b4;
    }
  }

  th {
    font-size: 12px;
    font-weight: bold;
    color: #8F8F9F;
    background-color: #F1F3F9;
  }

  &--input {
    input {
      padding: 8px 12px;
    }

    fieldset {
      border-color: #97CBE2 !important;
    }
  }

  tbody {
    tr:nth-child(even) {
      background-color: #F7FAFC;
    }
  }
}

.import-group-container {
  display: grid;
  grid-template-columns: calc(55% - 30px) 45%;
  column-gap: 30px;
  flex-grow: 1;
  overflow: hidden;

  .plan-group-name {
    font-size: 12px;
    font-weight: 500;
  }

  .plan-group-edit-icon {
    color: #bbbbc7;
  }

  .table-body-row {
    &[aria-checked="true"] {
      .plan-group-edit-icon {
        color: #8f8f8f;
      }
    }
  }

  .import-group-card {
    box-shadow: none;
    background-color: white;
    border-radius: 10px;
    display: flex;
    flex-direction: column;

    &--header {
      padding: 16px;
      color: #172B4D;
    }

    .group-card--title {
      font-size: 18px;
      font-weight: bold;
    }

    &--body {
      flex-grow: 1;
      overflow-y: auto;
    }

    &--footer {
      padding: 12px 24px;
      box-shadow: 0px -2px 3px #00000029;
    }
  }
}

.plan-group-popover {

  box-shadow: none;
}

.plan-group-input {
  width: 100%;
  input {
    padding: 8px;
    font-size: 12px;
  }

  fieldset {
    border-color: #7764E4 !important;
  }
}

.plan-group-btn {
  color: #7764E4;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.plan-group-btn--save {
  margin-left: 20px;
}

.warning-modal {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0px 0px 8px #00000029;
  background-color: #ffffff;
  border-radius: 20px;
  min-width: 620px;
  min-height: 400px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  &-title {
    color: #CE858F;
    margin-top: 34px !important;
    font: normal normal bold 16px/13px var(--font-family) !important;
  }

  &-icon {
    color: #CE858F;
    width: 100px;
    height: 100px;
  }

  &-btn {
    font-weight: bold;
    border-radius: 5px;
    border: 2px solid #7764E4;
    padding: 16px 26px;
    color: #7764E4;
    font-size: 16px !important;
    line-height: 20px;
    font-weight: bold !important;
    min-width: unset !important;
    box-sizing: border-box;

    &:hover {
      border: 2px solid #7764E4 !important;
    }

    &:first-child {
      margin-right: 20px;
    }

    &:last-child {
      color: white;
      background-color: #7764E4;

      &:hover {
        border: 2px solid #7764E4 !important;
        color: white;
        background-color: #7764E4;
      }
    }
  }

  &-btn--cancel {
    background-color: #7764E4;
    color: #ffffff;
  }
}

.box-modal {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600px;
  height: 330px;
  background-color: rgb(255, 255, 255);
  padding: 32px;
  box-shadow: 0px 0px 8px #00000029;
  border-radius: 20px;
  opacity: 1;

  .title-modal {
    font: normal normal bold 32px/46px var(--font-family) !important;
    letter-spacing: 0px;
    color: #3F7EAE;
    opacity: 1;
    text-align: center;
  }
}

.selected-group {
  background-color: #97CBE2 !important;
}

.modal-confirm {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0px 0px 8px #00000029;
  background-color: #ffffff;
  border-radius: 20px;
  width: 660px;
  max-height: 601px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 28px;
  margin-bottom: 28px;
  overflow-y: scroll;
  &-title {
    color: #2E4E76;
    font-size: 20px !important;
    font-weight: bold !important;
    font: normal normal bold 20px/13px var(--font-family);
    margin-bottom: 28px !important;
  }

  &-body {
    background-color: #F7F7FA;
    width: 100%;
    padding: 0px 60px;
    &-header {
      display: flex;
      align-items: center;
      color: #7764e4;
      width: 100%;
      margin-bottom: 24px;
      padding-top: 32px;
    }
  }

  .icon-close {
    position: absolute;
    right: 30px;
    top: 20px;
    font-size: 41px !important;
    color: #97CBE2;
  }

  &-icon {
    color: #CE858F;
    svg {
      width: 100px;
      height: 100px;
    }
  }

  &-btn {
    font-size: 16px;
    font-weight: bold;
    border-radius: 5px;
    border: 2px solid #7764E4;
    padding: 16px 28px;
    color: #7764E4;

    &:first-child {
      margin-right: 20px;
    }

    &:hover {
      background-color: #5841d9;
      color: #ffffff;
      border-color: #5841d9;
    }
  }

  &-btn--cancel {
    background-color: #7764E4;
    color: #ffffff;
  }

  &-grid {
    color: #2E4E76;
    font-size: 16px;
    font-weight: bold;
    &-text {
      margin-bottom: 24px !important;
    }
  }
}

.modal-confirm-line {
  height: 2px;
  background-color: #7764e4;
  width: 83%;
}

.custom-date-picker {
  width: 186px;
  height: 48px;
  font-size: 16px;
  color: #2E4E76;
  border: 1px solid #B7E1F1 !important;
  border-radius: 8px;
  opacity: 1;
  padding: 12px 24px !important;
}

.custom-datepicker-button {
  padding: 4px 12px;
  color: #2E4E76;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  background-color: #fff;
  outline: none;
  border: 0;
  text-align: start;
  min-height: 48px;
  display: flex;
  align-items: center;
  border: 1px solid #B7E1F1;
  cursor: pointer;
  transition: .24;
  width: 186px;
  position: relative;
  overflow: hidden;

  &:hover {
    background-color: #e8f2f6;
  }

  &:disabled {
    background-color: #E6E6EF;
    cursor: default;
  }

  .icon-choose {
    margin-left: 12px;
    margin-top: 2px;
    color: #fff;
    position: absolute;
    right: 0;
    top: -2px;
    width: 48px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #B7E1F1;
    border-top-right-radius: inherit;
    border-bottom-right-radius: inherit;
  }
}

.error-message {
  text-align: center;
  color: #D32F2F;
  font-size: 0.75rem;
  font-weight: normal;
}

.div-error {
  width: 186px;
}

.information {
  font-size: 100px;
}
