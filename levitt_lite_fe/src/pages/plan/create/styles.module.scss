.text-title {
  text-align: left;
  letter-spacing: 0px;
  color: #2E4E76;
  opacity: 1;
  border-bottom: 1px solid #97CBE2;
  opacity: 1;
  font-size: 20px !important;
  font-weight: bold !important;
  padding-bottom: 16px;
}

.text-box {
  text-align: left;
  letter-spacing: 0px;
  color: #2E4E76;
  opacity: 1;
  font-weight: 500 !important;
  font-size: 16px;
}

.text-select {
  color: #2E4E76;
  font-weight: 500;
  border-color: #97CBE2 !important;
  padding: 12px 24px;

  &.text-input {
    padding: 0;
    width: 100%;
    margin: 0 !important;

    input {
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 500;

      &:disabled {
        background-color: #E6E6EF;
      }
    }
  }

  div {
    padding: 0;
    color: #2E4E76;
  }

  svg {
    color: #2E4E76;
  }

  fieldset {
    border-color: #97CBE2 !important;
    border-radius: 8px;
  }
}

.tag {
  color: #2E4E76 !important;
}

.text-placeholder {
  color: #2E4E76;
  opacity: .5;
  }

.text-input {
  margin-top: 24px !important;
  width: 70%;
  margin-left: 7px !important;

  div {
    height: 45px;
    border-radius: 8px;
  }
}

.box-content {
  padding-left: 124px;

  .text-date {
    letter-spacing: 0px;
    color: #2E4E76;
    opacity: 1;
    text-align: justify;
  }
}

.box-button {
  margin-top: 24px;
  width: 280px;
  height: 56px;
  background: #7764E4 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #2C28281C;
  border-radius: 5px;
  opacity: 1;

  .text-button {
    letter-spacing: 0px;
    color: #FFFFFF;
    margin-top: 24px;
    cursor: pointer;
    width: 280px;
    height: 56px;
    background: #7764E4 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 6px #2C28281C;
    border-radius: 5px;
    opacity: 1;
    font-weight: bold;
    display: flex;
    align-content: stretch;
    flex-direction: column-reverse;
    justify-content: space-around;
    height: 100%;
    text-align: center;
  }
}

.box-modal {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600px;
  height: 330px;
  background-color: rgb(255, 255, 255);
  padding: 32px;
  box-shadow: 0px 0px 8px #00000029;
  border-radius: 20px;
  opacity: 1;

  .title-modal {
    font: normal normal bold 32px/46px var(--font-family) !important;
    letter-spacing: 0px;
    color: #3F7EAE;
    opacity: 1;
    text-align: center;
  }

  .text-modal {
    font: normal normal bold 12px/17px var(--font-family);
    letter-spacing: 0px;
    color: #CE858F;
    opacity: 1;
    text-align: center;
  }

  .icon-modal {
    color: #CE858F;
    font-size: 110px;
  }
}

.content-left {
  text-align: left;
  font: normal normal bold 20px/22px Gotham;
  letter-spacing: 0px;
  color: #1A1B35;
  opacity: 1;
  font-weight: bold !important;
  font-size: 20px !important;
  margin-right: 20px !important;
}

.content-center {
  letter-spacing: 0px;
  color: #1A1B35;
  opacity: 1;
  font-weight: medium !important;
  font-size: 13px !important;
  display: flex;
  align-items: center;
  margin-right: 10px !important;
}

.input-autocomplete {
  margin-top: 2.5rem !important;
  padding-top: 0!important;
  padding-bottom: 0!important;
  padding-right: 0!important;
  input {
    padding-right: 0!important;
    padding-left: 24px!important;
  }
}
