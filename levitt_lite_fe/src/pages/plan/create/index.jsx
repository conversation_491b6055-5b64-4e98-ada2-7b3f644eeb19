/* eslint-disable react-hooks/exhaustive-deps */
import React, {useEffect, useMemo, useState, useRef} from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Grid from "@mui/material/Grid";
import Paper from "@mui/material/Paper";
import Select from "@mui/material/Select";
import FormControl from "@mui/material/FormControl";
import FormGroup from "@mui/material/FormGroup";
import DatePicker, {registerLocale} from "react-datepicker";
import CircularProgress from "@mui/material/CircularProgress";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ErrorOutlineRoundedIcon from "@mui/icons-material/ErrorOutlineRounded";
import {
  Button,
  Checkbox,
  FormControlLabel,
  FormHelperText,
  IconButton,
  MenuItem,
  Popover,
  TextField,
} from "@mui/material";
import Modal from "@mui/material/Modal";
import {dataDays, dataMeals, dataPerks, dataRooms, PLAN_DATE_FORMAT,} from "../../../constants/index";
import { createPlan } from "../../../features/plans/plansSlice";
import styles from "./styles.module.scss";
import {useDispatch, useSelector} from "react-redux";
import moment from "moment";
import BreadCrumb from "../../../components/breadCrumb/index";
import * as Yup from "yup";
import {TEXT_REQUIRED} from "../../../constants/validation";
import {ErrorMessage, Formik} from "formik";
import ja from "date-fns/locale/ja";
import DatepickerButton from "../../../components/DatepickerButton";
import {Link, useLocation, useHistory} from "react-router-dom/cjs/react-router-dom";
import {getRecommendPlanList} from "../../../features/recommendPlan/recommendPlanSlice";
import EnhancedTable from "../../../components/EnhancedTable";
import PaginationPage from "../../../components/pagination";
import MoreVertRoundedIcon from "@mui/icons-material/MoreVertRounded";
import {getListHotelCompetition} from "../../../features/competitor/competitorSlice";
import {getHotels} from "../../../features/apiGetHotel";
import Autocomplete, {createFilterOptions} from "@mui/material/Autocomplete";
import http_common from "../../../http_common";

registerLocale("ja", ja);
const filter = createFilterOptions();
export default function CreatePlan() {
  const [open, setOpen] = useState(false);
  const [checkLoading, setCheckLoading] = useState(true);
  const [checkComplete, setCheckComplete] = useState(false);
  const [checkError, setCheckError] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const dispatch = useDispatch();
  const [selectedRow, setSelectedRow] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [currentCell, setCurrentCell] = useState(null);
  const [listRecommendPlan, setListRecommendPlan] = useState([]);
  const [hotels, setHotels] = useState([]);
  const [delaySearch, setDelaySearch] = useState();
  const intervalRef = useRef(null);
  const history = useHistory();

  useEffect(() => {
    dispatch(getListHotelCompetition());
  }, [])

  const inputHotelOnChange = async (query) => {
    const dataQuery = { search_param: query };
    clearTimeout(delaySearch);
    const search = setTimeout(async () => {
      const data = await fetchDataFromAPI(dataQuery);
      setHotels(data);
    }, 500);
    setDelaySearch(search);
  };

  const fetchDataFromAPI = async (dataQuery) => {
    try {
      const res = await dispatch(getHotels(dataQuery));
      setHotels(res.payload?.results?.data);
      return res.payload?.results?.data;
    } catch (error) {
      return [];
    }
  };

  function useQuery() {
    const { search } = useLocation();

    return useMemo(() => new URLSearchParams(search), [search]);
  }

  const isRecreate = useQuery().get("is_recreate");
  let recentRequestedPlan = undefined;
  if (isRecreate) {
    recentRequestedPlan = localStorage.getItem("planRequested");
    if (recentRequestedPlan) {
      recentRequestedPlan = JSON.parse(recentRequestedPlan);

      if (recentRequestedPlan.start_tarm) {
        recentRequestedPlan.start_tarm = moment(
          recentRequestedPlan.start_tarm
        ).toDate();
      }
      if (recentRequestedPlan.end_tarm) {
        recentRequestedPlan.end_tarm = moment(
          recentRequestedPlan.end_tarm
        ).toDate();
      }
    }
  }

  const { list: recommendPlans, paginate: recommendPlansPaginate } = useSelector(
    (state) => state.recommendPlan
  );

  const handleCloseCellAction = () => {
    setCurrentCell(null);
    setSelectedRow(null);
  };

  const handleOpenCellAction = (event, row) => {
    setCurrentCell(event.target);
    setSelectedRow(row);
  };

  useEffect(() => {
    const list = recommendPlans || [];
    const formatListDate = list.map((item) => {
      return {
        ...item,
        created_at: moment(item.created_at).format("YYYY.MM.DD"),
      };
    });
    setListRecommendPlan(formatListDate);
  }, [recommendPlans]);

  useEffect(() => {
    dispatch(
      getRecommendPlanList({
        page: currentPage,
      })
    );
  }, [currentPage, dispatch]);

  const formikConfig = {
    initialValues: {
      date_select_type:
        recentRequestedPlan?.date_select_type === false,
      room_type: recentRequestedPlan?.room_type || "",
      date_type: recentRequestedPlan?.date_type || "",
      meal: recentRequestedPlan?.meal || "",
      benefits: recentRequestedPlan?.benefits || "",
      hotel_id: recentRequestedPlan?.hotel_id || "",
      input_perks: recentRequestedPlan?.input_perks || "",
      start_tarm: recentRequestedPlan?.start_tarm || "",
      end_tarm: recentRequestedPlan?.end_tarm || "",
    },
    onSubmit: async (values, { setSubmitting, setErrors }) => {
      setSubmitting(true);
      const copyValues = { ...values };
      if (copyValues.date_select_type) {
        delete copyValues.start_tarm;
        delete copyValues.end_tarm;
      } else {
        copyValues.start_tarm = moment(copyValues.start_tarm).format(
          "YYYY-MM-DD"
        );
        copyValues.end_tarm = moment(copyValues.end_tarm).format(
          "YYYY-MM-DD"
        );
      }
      copyValues.hotel_id = values.hotel_id?.in_site_id
      localStorage.setItem("planRequested", JSON.stringify(copyValues));
      delete copyValues.date_select_type;
      await handleCreatePlan(copyValues);
      setSubmitting(false);
    },
    validationSchema: () => {
      return Yup.object({
        date_select_type: Yup.boolean(),
        room_type: Yup.string().required(TEXT_REQUIRED),
        date_type: Yup.string().required(TEXT_REQUIRED),
        meal: Yup.string().required(TEXT_REQUIRED),
        benefits: Yup.string().required(TEXT_REQUIRED),
        hotel_id: Yup.mixed().required(TEXT_REQUIRED),
        input_perks: Yup.string().when("benefits", {
          is: (benefits) => benefits === "その他",
          then: (schema) => schema.required(TEXT_REQUIRED),
          otherwise: (schema) => schema,
        }),
        start_tarm: Yup.mixed().when("date_select_type", {
          is: false,
          then: (schema) => schema.required(TEXT_REQUIRED),
          otherwise: (schema) => schema,
        }),
        end_tarm: Yup.mixed().when("date_select_type", {
          is: false,
          then: (schema) => schema.required(TEXT_REQUIRED),
          otherwise: (schema) => schema,
        }),
      });
    },
  };

  const handleCreatePlan = async (values) => {
    try {
      setOpen(true);
      setCheckLoading(true);
      setCheckError(false);
      setErrorMessage("");

      const res = await dispatch(createPlan(values));

      // Check if createPlan was rejected (error occurred)
      if (createPlan.rejected.match(res)) {
        const error = res.payload || res.error;
        let errorMessage = "プラン作成中にエラーが発生しました";

        // Check for specific error conditions
        if (error.statusCode === 429 || (error.message && error.message.includes("429"))) {
          errorMessage = "API呼び出し制限に達しました。しばらくしてから再試行してください。";
        } else if (error.statusCode === 400 || (error.message && error.message.includes("400"))) {
          errorMessage = "リクエストデータが無効です。入力内容を確認してください。";
        } else if (error.statusCode === 401 || (error.message && error.message.includes("401"))) {
          errorMessage = "認証に失敗しました。再度ログインしてください。";
        } else if (error.statusCode === 500 || (error.message && error.message.includes("500"))) {
          errorMessage = "サーバーエラーが発生しました。再試行してください。";
        } else if (error.message && error.message.includes("Network Error")) {
          errorMessage = "ネットワークエラーです。接続を確認してください。";
        } else if (error.message && (error.message.includes("API呼び出し制限") || error.message.includes("API call limit"))) {
          errorMessage = "API呼び出し制限に達しました。しばらくしてから再試行してください。";
        }

        setCheckLoading(false);
        setCheckError(true);
        setErrorMessage(errorMessage);
        return;
      }

      // Check if response contains error
      if (res.payload?.error) {
        setCheckLoading(false);
        setCheckError(true);
        setErrorMessage(res.payload.error);
        return;
      }

      const taskId = res.payload?.data?.task_id;
      
      // Check if we got a valid task ID
      if (!taskId) {
        setCheckLoading(false);
        setCheckError(true);
        setErrorMessage("プランタスクの作成に失敗しました。再試行してください。");
        return;
      }

      let pollCount = 0;
      const maxPollCount = 120; // 10 minutes (5 seconds * 120 = 600 seconds)

      intervalRef.current = setInterval(() => {
        pollCount++;

        if (pollCount >= maxPollCount) {
          // Timeout protection
          setCheckLoading(false);
          setCheckComplete(false);
          setCheckError(true);
          setErrorMessage("リクエストがタイムアウトしました。プラン作成に時間がかかりすぎています。再試行してください。");
          clearInterval(intervalRef.current);
          return;
        }

        checkTaskStatusCreatePlan(taskId);
      }, 5000);

      // Initial check
      checkTaskStatusCreatePlan(taskId);
    } catch (error) {
      console.error("error:", error);
      setCheckLoading(false);
      setCheckError(true);
      setErrorMessage("予期しないエラーが発生しました。再試行してください。");
    }
  };

  const checkTaskStatusCreatePlan = async (taskId) => {
    try {
      const response = await http_common.get(`/api/gpt_plans/get/${taskId}`);

      // HTTP interceptor returns response.data directly, so response is the data object
      if (response.status === 200) {
        // Task completed successfully
        setCheckLoading(false);
        setCheckComplete(true);
        setCheckError(false);
        setErrorMessage("");
        clearInterval(intervalRef.current);

        // Store the result in localStorage for redirect
        // Since interceptor returns response.data, response.data is the plan data
        if (response.data) {
          // response.data is already a string from backend, don't stringify again
          let planData;
          if (typeof response.data === 'string') {
            planData = response.data; // Already a JSON string
          } else {
            planData = JSON.stringify(response.data); // Convert object to string
          }

          localStorage.setItem("latestPlanResult", planData);
          console.log("Plan created successfully, stored in localStorage");
        } else {
          console.error("No data in response");
        }
      } else if (response.status === 202 || response.status === "pending") {
        // Task still pending
        console.log("Task still pending...");
      }
    } catch (error) {
      console.error("Error checking task status:", error);

      // Handle different types of errors
      if (error.response) {
        // Server responded with error status
        const statusCode = error.response.status;
        const errorMessage = error.response.data?.message || "エラーが発生しました";

        if (statusCode === 429) {
          // API call limit reached
          setCheckLoading(false);
          setCheckComplete(false);
          setCheckError(true);
          setErrorMessage("API呼び出し制限に達しました。しばらくしてから再試行してください。");
          clearInterval(intervalRef.current);
        } else if (statusCode === 500) {
          // Server error - task failed
          setCheckLoading(false);
          setCheckComplete(false);
          setCheckError(true);
          setErrorMessage(errorMessage);
          clearInterval(intervalRef.current);
        } else if (statusCode === 408) {
          // Request timeout
          setCheckLoading(false);
          setCheckComplete(false);
          setCheckError(true);
          setErrorMessage("リクエストがタイムアウトしました。再試行してください。");
          clearInterval(intervalRef.current);
        } else if (statusCode === 404) {
          // Task not found
          setCheckLoading(false);
          setCheckComplete(false);
          setCheckError(true);
          setErrorMessage("タスクが見つかりません。新しいプランを作成してください。");
          clearInterval(intervalRef.current);
        } else {
          // Other client errors
          setCheckLoading(false);
          setCheckComplete(false);
          setCheckError(true);
          setErrorMessage(errorMessage);
          clearInterval(intervalRef.current);
        }
      } else if (error.request) {
        // Network error
        setCheckLoading(false);
        setCheckComplete(false);
        setCheckError(true);
        setErrorMessage("ネットワークエラーです。接続を確認してください。");
        clearInterval(intervalRef.current);
      } else {
        // Other errors
        setCheckLoading(false);
        setCheckComplete(false);
        setCheckError(true);
        setErrorMessage("予期しないエラーが発生しました。");
        clearInterval(intervalRef.current);
      }
    }
  };

  const Placeholder = ({ children }) => {
    return <span className={styles["text-placeholder"]}>{children}</span>;
  };

  const redirectCreateRecommendPlan = () => {
    const planResult = localStorage.getItem("latestPlanResult");

    if (planResult) {
      try {
        // planResult is a JSON string containing plan data
        const planData = JSON.parse(planResult);

        // Check if planData is the plan object directly
        if (planData && typeof planData === 'object' && planData.hotel_id) {
          const planId = planData.hotel_id;
          const redirectUrl = `/suggestions/plans/result/${planId}`;

          history.push(redirectUrl, {
            planResult: planData
          });
        } else if (planData && typeof planData === 'object' && planData.title && planData.hotel_name) {
          // Fallback: use a generated ID or hotel name
          const planId = planData.hotel_name?.replace(/\s+/g, '-') || 'unknown';
          const redirectUrl = `/suggestions/plans/result/${planId}`;

          history.push(redirectUrl, {
            planResult: planData
          });
        } else {
          console.error("Invalid plan data structure:", planData);
          history.push("/suggestions/plans/create");
        }
      } catch (error) {
        console.error("Error parsing plan result:", error);
        history.push("/suggestions/plans/create");
      }
    } else {
      console.error("No planResult found in localStorage");
      history.push("/suggestions/plans/create");
    }
  };

  const LoadingModal = useMemo(
    () => (
      <Modal
        open={open}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <Box className={styles["box-modal"]}>
          {checkLoading ? (
            <>
              <Typography
                sx={{ textAlign: "center", marginTop: "50px" }}
                id="modal-modal-title"
                variant="h6"
                component="h2"
              >
                <CircularProgress />
              </Typography>
              <Typography
                className={styles["title-modal"]}
                id="modal-modal-description"
                sx={{ mt: 2 }}
              >
                作成中
              </Typography>
              <Typography
                className={styles["text-modal"]}
                id="modal-modal-description"
                sx={{ mt: 2 }}
              >
                作成中です。通常5秒から1分ほどかかります。
              </Typography>
            </>
          ) : (
            <>
              {checkError ? (
                <>
                  <Typography
                    sx={{ textAlign: "center" }}
                    id="modal-modal-title"
                    variant="h6"
                    component="h2"
                  >
                    <ErrorOutlineRoundedIcon className={styles["icon-modal"]} />
                  </Typography>
                  <Typography
                    className={styles["title-modal"]}
                    id="modal-modal-description"
                    sx={{ mt: 2 }}
                  >
                    エラー
                  </Typography>
                  <Typography
                    className={styles["text-modal"]}
                    id="modal-modal-description"
                    sx={{ mt: 2 }}
                  >
                    {errorMessage || "プラン作成中にエラーが発生しました。再試行してください。"}
                  </Typography>
                  <Box
                    sx={{ display: "flex", justifyContent: "center", gap: 2, mt: 3 }}
                  >
                    <Button
                      variant="contained"
                      color="primary"
                    onClick={() => {
                        setCheckError(false);
                        setErrorMessage("");
                      }}
                    >
                      再試行
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={() => {
                        setCheckError(false);
                        setErrorMessage("");
                      setOpen(false);
                    }}
                  >
                      キャンセル
                    </Button>
                  </Box>
                </>
              ) : (
                <>
                  <Typography
                    sx={{ textAlign: "center" }}
                    id="modal-modal-title"
                    variant="h6"
                    component="h2"
                  >
                    <CheckCircleIcon className={styles["icon-modal"]} />
                  </Typography>
                  <Typography
                    className={styles["title-modal"]}
                    id="modal-modal-description"
                    sx={{ mt: 2 }}
                  >
                    完了
                  </Typography>
                  <Box
                    sx={{ marginLeft: "25%" }}
                    className={styles["box-button"]}
                    onClick={() => redirectCreateRecommendPlan()}
                  >
                    <Typography
                      // component={Link}
                      className={styles["text-button"]}
                      sx={{ textDecoration: "none" }}
                    >
                      作成結果を見る
                    </Typography>
                  </Box>
                </>
              )}
            </>
          )}
        </Box>
      </Modal>
    ),
    [open, checkLoading, checkComplete, checkError, errorMessage]
  );

  const RecommendPlansTable = useMemo(() => {
    const headCells = [
      {
        id: "created_at",
        label: "作成日",
      },
      {
        id: "title",
        label: "プランタイトル",
      },
      {
        id: "id",
        label: "",
        options: {
          component: (row) => <CellAction row={row} />,
        },
      },
    ];

    const CellAction = ({ row }) => {
      return (
        <IconButton
          id={row.id}
          onClick={(event) => handleOpenCellAction(event, row)}
          sx={{
            boxShadow: "0px 3px 6px #2C28281C",
            color: "#172B4D",
          }}
        >
          <MoreVertRoundedIcon />
        </IconButton>
      );
    };

    return (
      <Paper
        sx={{
          display: "flex",
          flexDirection: "column",
          mt: "60px",
          width: "96%",
          marginLeft: "29px",
        }}
      >
        <Typography
          sx={{
            fontWeight: 700,
            p: "30px 42px",
            color: "#172B4D",
            fontSize: "18px",
          }}
        >
          過去に作成したプラン
        </Typography>
        <Box>
          <EnhancedTable
            selectAble={false}
            headCells={headCells}
            rows={listRecommendPlan}
          />
        </Box>
        {recommendPlansPaginate?.total_pages > 1 && (
          <PaginationPage
            count={recommendPlansPaginate.total_pages}
            onChange={(event, value) => setCurrentPage(value)}
            variant="outlined"
            shape="rounded"
          />
        )}
      </Paper>
    );
  }, [listRecommendPlan, recommendPlansPaginate.total_pages]);

  return (
    <>
      <Paper
        sx={{
          backgroundColor: "#F1F3F9",
          flexGrow: 1,
          overflow: "auto",
          p: 4,
          boxShadow: "0px 0px 0px 0px rgba(0,0,0,0) !important",
        }}
      >
        <Box>
          <BreadCrumb
            content="プラン作成"
            textRoutes={["ダッシュボード", "提案を受ける", "プラン作成"]}
          />
          <Formik
            enableReinitialize={true}
            validateOnChange={false}
            initialValues={formikConfig.initialValues}
            onSubmit={formikConfig.onSubmit}
            validationSchema={formikConfig.validationSchema}
          >
            {({
              handleChange,
              values,
              errors,
              handleSubmit,
              setFieldValue,
              isSubmitting,
            }) => (
              <>
                <Paper sx={{ p: 5, display: "flex", flexDirection: "column" }}>
                  <Typography className={styles["text-title"]}>
                    強調したい要素を選ぶ
                  </Typography>
                  <Box sx={{ marginTop: "28px" }}>
                    <Grid container spacing={2}>
                      <Grid item xs={4}>
                        <Typography className={styles["text-box"]}>
                          強調したい要素を選んでください。
                          特にない場合は、「強調したい要素がない」
                          にチェックを入れてください。
                        </Typography>
                      </Grid>
                      <Grid item xs={8}>
                        <FormControl sx={{ width: "100%" }}>
                          <Select
                            className={styles["text-select"]}
                            displayEmpty
                            name="room_type"
                            error={!!errors.room_type}
                            onChange={handleChange}
                            value={values.room_type}
                            sx={{
                              borderRadius: "8px",
                              height: "45px",
                              background: "#FFFF 0% 0% no-repeat padding-box",
                            }}
                            renderValue={
                              !!values.room_type
                                ? undefined
                                : () => (
                                    <Placeholder>
                                      すべての客室タイプ
                                    </Placeholder>
                                  )
                            }
                          >
                            {dataRooms?.map(
                              (item, index) =>
                                item && (
                                  <MenuItem
                                    key={index}
                                    className={styles["tag"]}
                                    value={item}
                                  >
                                    {item}
                                  </MenuItem>
                                )
                            )}
                          </Select>
                          {!!errors.room_type && (
                            <FormHelperText sx={{ color: "#d32f2f" }}>
                              客室タイプを選択してください。
                            </FormHelperText>
                          )}
                        </FormControl>
                        <FormControl sx={{ width: "100%", mt: 2.5 }}>
                          <Select
                            className={styles["text-select"]}
                            displayEmpty
                            value={values.date_type}
                            name="date_type"
                            onChange={handleChange}
                            sx={{
                              borderRadius: "8px",
                              height: "45px",
                              background: "#FFFF 0% 0% no-repeat padding-box",
                            }}
                            error={!!errors.date_type}
                            renderValue={
                              !!values.date_type
                                ? undefined
                                : () => <Placeholder>すべての曜日</Placeholder>
                            }
                          >
                            {dataDays?.map(
                              (item, index) =>
                                item && (
                                  <MenuItem
                                    key={index}
                                    className={styles["tag"]}
                                    value={item}
                                  >
                                    {item}
                                  </MenuItem>
                                )
                            )}
                          </Select>
                          {errors.date_type && (
                            <FormHelperText sx={{ color: "#d32f2f" }}>
                              曜日を選択してください。
                            </FormHelperText>
                          )}
                        </FormControl>
                        <FormControl sx={{ width: "100%", mt: 2.5 }}>
                          <Select
                            className={styles["text-select"]}
                            displayEmpty
                            name="meal"
                            value={values.meal}
                            onChange={handleChange}
                            error={!!errors.meal}
                            sx={{
                              borderRadius: "8px",
                              height: "45px",
                              background: "#FFFF 0% 0% no-repeat padding-box",
                            }}
                            renderValue={
                              !!values.meal
                                ? undefined
                                : () => (
                                    <Placeholder>食事の提供も可能</Placeholder>
                                  )
                            }
                          >
                            {dataMeals?.map(
                              (item, index) =>
                                item && (
                                  <MenuItem
                                    key={index}
                                    className={styles["tag"]}
                                    value={item}
                                  >
                                    {item}
                                  </MenuItem>
                                )
                            )}
                          </Select>
                          {errors.meal && (
                            <FormHelperText sx={{ color: "#d32f2f" }}>
                              食事の提供を選択してください。
                            </FormHelperText>
                          )}
                        </FormControl>
                        <FormControl sx={{ width: "100%", mt: 2.5 }}>
                          <Select
                            name="benefits"
                            className={styles["text-select"]}
                            displayEmpty
                            onChange={(e) => {
                              if (e.target.value !== "その他") {
                                setFieldValue("input_perks", "");
                              }
                              return handleChange(e);
                            }}
                            error={!!errors.benefits}
                            value={values.benefits}
                            sx={{
                              borderRadius: "8px",
                              height: "45px",
                              background: "#FFFF 0% 0% no-repeat padding-box",
                            }}
                            renderValue={
                              !!values.benefits
                                ? undefined
                                : () => <Placeholder>すべての特典</Placeholder>
                            }
                          >
                            {dataPerks?.map(
                              (item, index) =>
                                item && (
                                  <MenuItem
                                    key={index}
                                    className={styles["tag"]}
                                    value={item}
                                  >
                                    {item}
                                  </MenuItem>
                                )
                            )}
                          </Select>
                          {errors.benefits && (
                            <FormHelperText sx={{ color: "#d32f2f" }}>
                              特典を選択してください。
                            </FormHelperText>
                          )}
                        </FormControl>
                        <FormControl sx={{ width: "100%", mt: 2.5 }}>
                          <TextField
                            disabled={values.benefits !== "その他"}
                            variant="outlined"
                            placeholder="特典でその他を選択した場合にのみ入力"
                            className={`${styles["text-select"]} ${styles["text-input"]}`}
                            value={values.input_perks}
                            helperText={errors.input_perks}
                            error={!!errors.input_perks}
                            name="input_perks"
                            onChange={handleChange}
                          />
                        </FormControl>
                        <FormControl sx={{ width: "100%", mt: 2.5, mb: 7.5 }}>
                          <Autocomplete
                            freeSolo
                            sx={{ width: "100%", mt: 2.5 }}
                            helperText={errors.hotel_id}
                            disablePortal
                            disableClearable={true}
                            className={`${styles["input-autocomplete"]} ${styles["text-select"]} ${styles["text-input"]}`}
                            options={hotels}
                            name="hotel_id"
                            value={values.hotel_id}
                            onChange={(event, newValue) => {
                              setFieldValue("hotel_id", newValue)
                            }}
                            filterOptions={(options, params) => {
                              return filter(options, params);
                            }}
                            getOptionLabel={(option) => {
                              if (typeof option === "string") {
                                return option;
                              }
                              if (option?.inputValue) {
                                return option?.inputValue;
                              }
                              return option?.name;
                            }}
                            onInputChange={(event, newInputValue) => {
                              inputHotelOnChange(newInputValue);
                            }}
                            onFocus={() => {
                              inputHotelOnChange("");
                            }}
                            renderOption={(props, option) => (
                              <Box component="li" {...props} sx={{ p: "10px 24px" }}>
                                <Box
                                  sx={{
                                    width: "100%",
                                    color: "#2E4E76",
                                    fontWeight: "bold",
                                    display: "flex",
                                  }}
                                >
                                  <Typography
                                    component={"span"}
                                    sx={{
                                      textAlign: "left",
                                      minWidth: "70%",
                                      fontWeight: "bold",
                                      fontSize: "16px",
                                    }}
                                  >
                                    {option.name}
                                  </Typography>
                                  <Typography
                                    component={"span"}
                                    sx={{
                                      textAlign: "right",
                                      fontWeight: "bold",
                                      fontSize: "16px",
                                    }}
                                  >
                                    {option.address}
                                  </Typography>
                                </Box>
                              </Box>
                            )}

                            renderInput={(params) => (
                              <Box sx={{ width: "100%" }}>
                                <TextField
                                  sx={{
                                    width: "100%",
                                    "& .MuiOutlinedInput-root": {
                                      p: 0,
                                    },
                                    "& .MuiAutocomplete-endAdornment": {
                                      right: "3px",
                                    },
                                  }}
                                  label={values.name}
                                  value={values.in_site_id}
                                  {...params}
                                  className={styles["competitor--card--input"]}
                                  placeholder="宿泊施設名を入力してください"
                                />
                              </Box>
                            )}
                          />
                          {errors.hotel_id && (
                            <FormHelperText sx={{ color: "#d32f2f" }}>
                              ホテルを選択してください
                            </FormHelperText>
                          )}
                        </FormControl>
                      </Grid>
                    </Grid>
                  </Box>
                  <Typography className={styles["text-title"]}>
                    期間を決める
                  </Typography>
                  <Box sx={{ marginTop: "28px" }}>
                    <Grid container spacing={2}>
                      <Grid item xs={4}>
                        <Typography className={styles["text-box"]}>
                          掲載期間、販売期間を指定してください。 特に
                          指定がない場合は、「期間を定めない」
                          にチェックを入れてください。
                        </Typography>
                      </Grid>
                      <Grid item xs={8}>
                        <FormGroup>
                          <FormControlLabel
                            control={
                              <Checkbox
                                name="date_select_type"
                                onChange={handleChange}
                                checked={values.date_select_type}
                              />
                            }
                            label={
                              <Typography
                                variant="h7"
                                style={{ color: "#2E4E76", fontWeight: 500 }}
                              >
                                期間を定めない
                              </Typography>
                            }
                          />
                        </FormGroup>
                        <Typography
                          className={styles["text-date"]}
                          sx={{ mt: 3.5, fontWeight: 700 }}
                        >
                          販売期間
                        </Typography>
                        <Box
                          sx={{
                            display: "grid",
                            marginTop: "12px",
                            width: "100%",
                            gap: "16px",
                            alignItems: "center",
                            gridTemplateColumns: "1fr auto 1fr",
                          }}
                        >
                          <Box
                            sx={{ flexGrow: 1 }}
                            className="custom-datepicker-container"
                          >
                            <DatePicker
                              locale="ja"
                              selected={values.start_tarm}
                              disabled={values.date_select_type}
                              dateFormat={PLAN_DATE_FORMAT}
                              onChange={(date) => {
                                if (date) {
                                  const formattedDate = moment(date).toDate();
                                  setFieldValue(
                                    "start_tarm",
                                    formattedDate
                                  );
                                } else {
                                  setFieldValue("start_tarm", null);
                                }
                              }}
                              calendarClassName="custom-datepicker"
                              className="custom-datepicker-container"
                              customInput={<DatepickerButton />}
                            />
                            <ErrorMessage
                              className="error-text"
                              name="start_tarm"
                              component="div"
                            />
                          </Box>
                          <Box>〜</Box>
                          <Box
                            sx={{ flexGrow: 1 }}
                            className="custom-datepicker-container"
                          >
                            <DatePicker
                              locale="ja"
                              disabled={values.date_select_type}
                              selected={values.end_tarm}
                              dateFormat={PLAN_DATE_FORMAT}
                              onChange={(date) => {
                                if (date) {
                                  const formattedDate = moment(date).toDate();
                                  setFieldValue("end_tarm", formattedDate);
                                } else {
                                  setFieldValue("end_tarm", null);
                                }
                              }}
                              calendarClassName="custom-datepicker"
                              customInput={<DatepickerButton />}
                            />
                            <ErrorMessage
                              className="error-text"
                              name="end_tarm"
                              component="div"
                            />
                          </Box>
                        </Box>
                        <Box className={styles["box-button"]}>
                          <Typography
                            onClick={handleSubmit}
                            component={Button}
                            disabled={isSubmitting}
                            className={styles["text-button"]}
                          >
                            上記の内容でプランを造成する
                          </Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  </Box>
                </Paper>
              </>
            )}
          </Formik>
        </Box>

        {LoadingModal}
      </Paper>
      {RecommendPlansTable}
      <Popover
        open={!!currentCell}
        anchorEl={currentCell}
        onClose={handleCloseCellAction}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        sx={{
          "& .MuiPopover-paper": {
            borderRadius: 0,
            backgroundColor: "#2E4E76",
            minWidth: "130px",
            p: "10px 12px",
          },
        }}
      >
        <Typography
          sx={{
            color: "#FFFFFF",
            textDecoration: "none",
            fontSize: "14px",
            fontWeight: "bold",
          }}
          component={Link}
          to={`/suggestions/plans/result/${selectedRow?.id}`}
        >
          詳細を見る
        </Typography>
      </Popover>
    </>
  );
}
