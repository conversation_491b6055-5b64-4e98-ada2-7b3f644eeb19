$main_color: #2E4E76;
$footer_height: 60px;
$header_height: 70px;

.content-login {
  text-align: center;
  color: #1a1b35;
  font: normal normal bold 24px/24px var(--font-family) !important;
  padding-bottom: 10px;
}

.content-bottom-login {
  margin-top: 12px;
  text-align: center;
  font: normal normal 500 14px/24px var(--font-family) !important;
  color: $main_color;
}

.box-continue {
  display: flex;
  justify-content: center;
  margin-top: 14px;
  border-radius: 5px;

  .text-button {
    border: 1px solid $main_color;
    opacity: 1;
    padding: 16px 0;
    font: bold 16px/20px var(--font-family),
    sans-serif;
    color: #ffff;
    width: 30%;
    text-align: center;
    background: $main_color 0% 0% no-repeat padding-box;
    border-radius: 5px;
    cursor: pointer;

    &:disabled {
      opacity: 0.6 !important;
      cursor: default !important;
    }
  }
}

.link {
  color: #3f7eae;
  font: 500 14px/18px var(--font-family);
}

.box-main-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding-top: 80px;
  flex-grow: 1;

  .error-text {
    color: #CE858F;
    font: 300 14px/18px var(--font-family);
  }
}

.custom-field {
  width: 30%;
  margin-top: 30px !important;
  font: bold 16px/18px var(--font-family);
}

.more-action-information {
  text-align: center;
  margin-top: 20px;
  color: #1A1B35;
  font: 500 14px/18px var(--font-family);
}

.custom-error-message {
  color: #d32f2f;
  font-size: 0.75rem;
  font-weight: 400;
  line-height: 1.66;
  padding-top: 10px;
  text-align: center;
}
