.text-title {
  color: #2e4e76;
  border-bottom: 2px solid #97cbe2;
  height: 50px;
  font: 700 20px/13px var(--font-family) !important;
  display: flex;
  align-items: normal;
  margin-bottom: 35px !important;
}

$width_box: 18vw;
.box-button-edit {
  background: #ffffff;
  box-shadow: 0px 3px 6px #00000052;
  border-radius: 10px;
  cursor: pointer;
  width: $width_box;
  height: calc($width_box * 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin-bottom: 30px;

  .icon-common {
    width: 72px;
    height: 72px;
    object-fit: cover;
    color: #2e4e76;
    margin-bottom: 15px;
    font-size: 100px;
  }

  .text-button-edit {
    font: bold 20px/29px var(--font-family) !important;
    color: #2e4e76;
  }
}

.title-sidebar {
  text-align: left;
  font: bold 16px/24px var(--font-family),
  sans-serif !important;
  letter-spacing: 0px;
  color: #172B4D;
  opacity: 0.8;

  span {
    font: bold 16px/24px var(--font-family),
    sans-serif !important;
  }
}

.coming-soon-feature {
  opacity: 0.6;
  box-shadow: none;
  cursor: not-allowed;
}

.no-border {
  border: 0 !important;
}

.icon-right {
  color: #BBBBC7;
  font-size: 16px !important;
}

.icon-right-disabled {
  color: #2E4E76;
  font-size: 16px !important;
}
