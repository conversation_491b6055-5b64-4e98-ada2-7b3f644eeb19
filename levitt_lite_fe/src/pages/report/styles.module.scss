.box-header {
  padding-bottom: 12px;
  width: 100%;
  border-bottom: 1px solid #97CBE2;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .icon-download {
    width: 28px;
    height: 28px;
    color: #2E4E76;
  }
}

.custom-datepicker-button {
  min-width: 280px;
  padding: 4px 12px;
  color: #2E4E76;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  background-color: #E6E6EF;
  outline: none;
  border: 0;
  text-align: start;
  min-height: 32px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: .24;

  &:hover {
    background-color: #97CBE2;
  }

  .icon-choose {
    margin-left: 12px;
    color: #8F8F9F;
  }
}

.report-container {
  display: grid;
  grid-template-columns: 70% auto;
  column-gap: 40px;

  .report-box {
    box-shadow: 0px 3px 6px #00000029;
    background: #FFFFFF;
  }
}

.report-box-header {
  color: #2E4E76;
  padding: 12px 32px;
  border-bottom: 1px solid #3F7EAE;
  display: flex;
  align-items: center;
  justify-content: space-between;

  p {
    margin: 0;
  }

  &-title {
    font-size: 16px;
    font-weight: bold;
  }

  &-date {
    color: #3F7EAE;
    font-size: 12px;
    font-weight: 500;
  }
}

.report-list {
  color: #172B4D;
  font-weight: 500;
}

.report-items {
  padding: 20px 32px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;

  .report-item {
    box-shadow: 0px 0px 4px #00000052;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;

    &-title {
      font-weight: bold;
      color: #2E4E76;
      font-size: 12px;

      &--span {
        font-size: 10px;
        color: #3F7EAE;
        margin-left: 12px;
      }
    }

    &-subtitle {
      font-size: 10px;
      color: #172B4D;
    }
  }
}

.report-table {
  width: 100%;
  border-collapse: collapse;

  th {
    font-weight: bold;
    text-align: center;

    &:first-child {
      color: #172B4D;
      text-align: start;
      width: 55%;
    }

    &:nth-child(2) {
      color: #7764E4;
    }

    &:nth-child(3) {
      color: #CE858F;
    }
  }

  td {
    font-weight: 500;

    &:first-child {
      width: 55%;
      max-width: 55%;
    }

    p {
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      margin: 0;
    }

    &:not(:first-child) {
      text-align: center;
    }

    &.text-center {
      text-align: center;
    }
  }

  th,
  td {
    font-size: 10px;
    color: #2E4E76;
  }

  tbody {
    background-color: #F8F9FD;
    td,
    th {
      background-color: #F8F9FD;
    }
  }

  .detail-row {
    td:first-child {
      padding-top: 12px;
    }
  }
}

.report-menu-items {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 40px 0 30px 0;

  .selected {
    background-color: #F0F0F7;
    color: #3F7EAE;
  }
}

.report-box-footer {
  border-top: 1px solid #3F7EAE;
  padding: 16px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &-text {
    font-size: 12px;
    color: #2E4E76;
    font-weight: bold;
  }

  .logo {
    width: 120px;
  }
}

.report-table--ranking {
  th,
  td {
    font-size: 12px;
    padding: 10px 16px;
  }

  th {
    color: #fff !important;
    background-color: #3F7EAE;
  }
}

.report-ranking {
  padding: 20px 32px;
}

.grouped-labels {
  display: flex;
  width: 100%;
  color: #172B4D;
  justify-content: flex-end;
  margin-top: 12px;
  font-size: 8px;
  font-weight: bold;

  &-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    box-shadow: none !important;
    justify-content: flex-start;
    align-items: flex-start;
    padding: 0 !important;
    span {
      font-size: 8px;
    }
  }

  &-grid-multiple {
    grid-template-columns: repeat(5, 1fr);
  }

  .grouped-label {
    display: flex;
    align-items: center;
  }

  .grouped-label-color {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin: 0 8px;
  }
}

.report-table--by-days {
  th {
    white-space: nowrap;
    text-align: center;
    padding: 10px 16px;
  }

  td {
    border-bottom: 1px solid #B7E1F1;
    padding: 2px;

    &:first-child {
      padding-left: 16px;
      color: #2E4E76;
      font-weight: 500;
      padding-bottom: 0;
    }
  }

  .cell-date {
    width: 100%;
    height: 37px;
    background-color: #4141FF;
  }

  .cell-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 40px!important;
  }

  .chart-container {
    max-width: 100%;
  }
}
