.competitor-container {
  &--title {
    font-weight: bold;
    color: #2E4E76;
    display: flex;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid #97CBE2;
  }
}

.competitor--card {
  padding: 32px 40px;
  border-radius: 10px;
  box-shadow: 0px 3px 6px #2C28281C;

  &--title {
    font-weight: bold;
    color: #2E4E76;
    font-size: 20px;
  }

  &--subtitle {
    color: #8F8F9F;
    font-size: 16px;
    font-weight: bold;
  }

  .input-container {
    margin-top: 32px;
    display: flex;
    min-width: 60%;
  }

  &--input {
    color: #2E4E76;
    font-size: 16px;
    width: 100%;

    &::before,
    &::after {
      display: none;
    }

    input {
      border-top-left-radius: 8px;
      border-bottom-left-radius: 8px;
      padding: 12px 32px !important;
      border: 1px solid #B7E1F1;
      line-height: 1;
      font-weight: bold;
      color: #2E4E76;
    }

    fieldset {
      border: 0;
    }
  }

  &--icon {
    background-color: #7764E4;
    padding: 12px 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #ffffff;
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
    cursor: pointer;
    position: relative !important;
  }
}

.competitor--table {
  tr {
    &:nth-child(even) {
      td {
        background-color: #F8F8F8;
      }
    }

    td {
      color: #2E4E76;
      font-weight: bold;
      padding: 12px 28px;
      border: 0;

      &:last-child {
        color: #F53C56;
      }
    }
  }
}

.confirm-delete {
  box-shadow: 0px 0px 8px #00000052;
  border-radius: 8px;
  padding: 20px 32px;
  display: flex;
}
