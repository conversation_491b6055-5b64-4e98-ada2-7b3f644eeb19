.home-center {
  background: #FFFFFF 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #2C28281C;
  border-radius: 10px;
  opacity: 1;
  padding-left: 0px !important;
  padding-right: 0px !important;

  .box-header {
    display: flex;
    align-items: center;

    .text-icon {
      width: 30px;
      height: 30px;
      margin-top: -3px;
      margin-left: 20px;
      color: #2E4E76;
    }
  }

  .text-header-home {
    text-align: left;
    font-weight: bold !important;
    font-size: 18px !important;
    letter-spacing: 0px;
    color: #2E4E76;
    opacity: 1;
    padding: 21px;
  }

  .text-head {
    color: #2E4E76;
    font: bold 16px/13px var(--font-family);
  }

  .content-head {
    display: flex;
  }

  .text-left {
    color: #2E4E76;
    padding: 40px !important;
    font: bold 16px/13px var(--font-family);
  }

  .text-field {
    width: 360px;
    margin-top: 10px;

    div {
      background: #F7F7F8 0% 0% no-repeat padding-box;
      border-radius: 4px;
    }

    fieldset {
      border-color: #FFFFFF;
    }

    input {
      height: 8px;
      color: #2E4E76;
      font: 500 16px/22px var(--font-family);
    }

    label {
      margin-top: -7px;
      font: 500 16px/22px var(--font-family);
    }
  }

  .text-select {
    width: 360px;
    background: #F7F7F8 0% 0% no-repeat padding-box;
    border-radius: 4px;
    height: 40px;
    color: #2E4E76;
    font: 500 16px/22px var(--font-family);
    margin-top: 10px;


    fieldset {
      border-color: #FFFFFF;
    }
  }

  .box-group {
    flex-direction: row;
    margin-top: 9px;

    .text-group {
      font: 500 16px/13px var(--font-family);
      letter-spacing: 0px;
      color: #2E4E76;
    }
  }

  .box-button {
    display: flex;
    gap: 32px;
    margin-left: 25%;
    padding-bottom: 30px;
    width: 65%;
  }

  .text-cancel {
    box-shadow: 0px 3px 6px #2C28281C;
    border: 2px solid #7764E4;
    border-radius: 5px;
    font: 500 16px/13px var(--font-family);
    color: #7764E4;
    padding: 15px;
    line-height: 19px;
    cursor: pointer;
    white-space: nowrap;
  }

  .text-save {
    background: #7764E4 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 6px #2C28281C;
    border-radius: 5px;
    padding: 15px;
    font: 500 16px/13px var(--font-family);
    color: #FFFFFF;
    line-height: 19px;
    cursor: pointer;
    white-space: nowrap;
  }

  .text-delete {
    box-shadow: 0px 3px 6px #2C28281C;
    border: 1px solid  #CE858F;
    border-radius: 5px;
    font: 500 16px/13px var(--font-family);
    color:  #CE858F;
    padding: 15px;
    cursor: pointer;
    line-height: 19px;
    margin-left: 58%;
    white-space: nowrap;
  }
}

.box-delete .text-off {
  color: #7764E4;
  font: 500 16px/23px var(--font-family);
  cursor: pointer;
}

.box-delete .delete {
  color: #CE858F;
  font: 500 16px/23px var(--font-family);
  cursor: pointer;
}
