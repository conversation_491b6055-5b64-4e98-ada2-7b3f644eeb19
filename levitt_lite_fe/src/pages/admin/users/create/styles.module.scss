.box-header-email {
  display: flex;
  justify-content: space-between;
  padding-left: 80px;
  padding-right: 80px;
  padding-top: 22px;

  .box-right-email {
    display: flex;

    .text-right-email {
      color: var(--unnamed-color-2e4e76);
      display: flex;
      align-items: center;
      text-align: left;
      font: normal normal bold 16px/20px var(--font-family) !important;
      letter-spacing: 0px;
      color: #2e4e76;
      opacity: 1;
      margin-right: 25px;
    }

    .button-right-email {
      border: 1px solid #2e4e76;
      opacity: 1;
      padding: 15px 25px 15px 25px;
      font: normal normal bold 16px/20px var(--font-family) !important;
      letter-spacing: 0px;
      color: #2e4e76;
    }
  }

  .title-email {
    text-align: left;
    letter-spacing: 0px;
    color: #2e4e76;
    opacity: 1;
    display: flex;
    align-items: center;
    font: normal normal bold 24px/20px var(--font-family) !important;
  }
}

.box-content {
  height: 880px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  .error-text {
    color: #CE858F;
    font: 500 14px/18px var(--font-family);
  }
}

.content-email {
  text-align: center;
  font: normal normal bold 24px/24px var(--font-family) !important;
  letter-spacing: 0px;
  color: #1a1b35;
  opacity: 1;
}

.content-email {
  display: flex;
  width: 100%;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  font-size: 24px !important;
}

.content-bottom-email {
  margin-top: 8px !important;
  text-align: center;
  letter-spacing: 0px;
  color: #1a1b35;
  opacity: 1;
  font-weight: bold !important;
  width: 25%;
  font-size: 12px !important;
}

.link {
  letter-spacing: 0px;
  color: #3f7eae;
  font-weight: bold;
}

.box-continue {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  border-radius: 5px;

  .text-button {
    border: 1px solid #2e4e76;
    opacity: 1;
    padding: 15px 25px 15px 25px;
    font: normal normal bold 16px/20px var(--font-family) !important;
    letter-spacing: 0px;
    color: #ffff;
    width: 20%;
    text-align: center;
    background: #2e4e76 0% 0% no-repeat padding-box;
    border-radius: 5px;
    cursor: pointer;
  }
}

.footer-email {
  border-top: 1px solid #bbbbc7;
  position: absolute;
  bottom: 0;
  width: 100%;
  text-align: center;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;

  .text-footer {
    font: normal normal medium 16px/24px var(--font-family);
    letter-spacing: 0px;
    color: #bbbbc7;
    opacity: 1;
    margin-right: 10px;
  }

  .text-footer-right {
    font: normal normal medium 16px/24px var(--font-family);
    letter-spacing: 0px;
    color: #bbbbc7;
    opacity: 1;
  }
}
