.home-center {
  background: #FFFFFF 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #2C28281C;
  border-radius: 10px;
  opacity: 1;
  padding-left: 0px !important;
  padding-right: 0px !important;
  margin-top: 50px;

  .box-header {
    display: flex;
    align-items: center;

    .text-icon {
      width: 30px;
      height: 30px;
      margin-top: -3px;
      margin-left: 20px;
      color: #2E4E76;
    }
  }

  .text-header-home {
    text-align: left;
    color: #2E4E76;
    opacity: 1;
    padding: 21px;
    font: 500 16px/20px var(--font-family);
  }

  .text-header {
    text-align: left;
    color: #2E4E76;
    opacity: 1;
    padding: 21px;
    font: 500 16px/23px var(--font-family);
  }

  .text-head {
    color: #2E4E76;
    font: 500 16px/13px var(--font-family);
  }

  .content-head {
    display: flex;
  }

  .text-left {
    color: #2E4E76;
    padding: 36px !important;
    font: 500 16px/13px var(--font-family);
  }

  .text-number {
    color: #2E4E76;
    font: 500 16px/13px var(--font-family);
  }

  .text-field {
    width: 360px;
    margin-top: 6px;

    div {
      background: #F7F7F8 0% 0% no-repeat padding-box;
      border-radius: 4px;
      height: 40px;
    }

    input {
      font: 500 16px/22px var(--font-family);
      color: #2E4E76;
    }

    fieldset {
      border-color: #FFFFFF;
    }
  }

  .text-select {
    width: 180px;
    background: #F7F7F8 0% 0% no-repeat padding-box;
    height: 40px;
    border-radius: 4px;
    color: #2E4E76;
    font: 500 16px/22px var(--font-family);
    margin-right: 20px;

    fieldset {
        border-color: #FFFFFF;
    }
}

  .box-group {
    flex-direction: row;
    margin-top: 9px;

    .text-group {
      font: 500 16px/13px var(--font-family);
      letter-spacing: 0px;
      color: #2E4E76;
    }
  }

  .box-button {
    display: flex;
    gap: 32px;
    margin-left: 17%;
    padding-bottom: 30px;
    width: 65%;
  }

  .text-cancel {
    box-shadow: 0px 3px 6px #2C28281C;
    border: 2px solid #7764E4;
    border-radius: 5px;
    font: 500 16px/13px var(--font-family);
    color: #7764E4;
    padding: 15px;
    line-height: 19px;
    cursor: pointer;
  }

  .text-save {
    background: #7764E4 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 6px #2C28281C;
    border-radius: 5px;
    padding: 15px;
    font: 500 16px/13px var(--font-family);
    color: #FFFFFF;
    line-height: 19px;
    cursor: pointer;
  }
}
