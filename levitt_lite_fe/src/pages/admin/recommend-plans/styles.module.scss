.box-button-choose {
  margin-top: 24px;
  width: 100%;
  height: 100%;
  margin-right: 80px;
  padding: 30px;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000052;
  border-radius: 10px;
  opacity: 1;

  .icon-choose {
    height: 100px;
    width: 100%;
    color: #2e4e76;
  }

  .button-choose {
    background: #e6e6ef 0% 0% no-repeat padding-box;
    box-shadow: 0px 0px 8px #00000029;
    border-radius: 4px;
    opacity: 1;
    width: 50%;
    letter-spacing: 0px;
    color: #3f7eae;
    text-align: center;
    margin-left: 82px;
  }

  .text-button-content {
    text-align: left;
    font-size: 12px !important;
    letter-spacing: 0px;
    color: #4D4F5C;
    opacity: 1;
    margin-right: 20px;
  }

  .text-button-right {
    text-align: left;
    font-size: 13px;
    font-weight: bold;
    letter-spacing: 0px;
    color: #2DCE98;
    opacity: 1;
  }

  .box-head {
    display: flex;
    justify-content: space-between;
  }

  .text-button-choose {
    text-align: left;
    font: bold 12px/12px var(--font-family),
    sans-serif;
    letter-spacing: 0px;
    color: #8898aa;
    opacity: 1;
  }

  .title-button {
    text-align: left;
    font: bold 12px/12px var(--font-family),
    sans-serif;
    letter-spacing: 0px;
    color: #4D4F5C;
    opacity: 1;
  }
}

.box-content {
  margin-top: 24px;
  height: 100%;
  padding: 11px;
  opacity: 1;
  background: #FFFFFF 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #2C28281C;
  border: 1px solid #7764E4;
  border-radius: 5px;

  .text-content {
    letter-spacing: 0px;
    font-size: 12px !important;
    color: #7764E4;
    opacity: 1;
    font-weight: bold;
    display: flex;
    align-content: stretch;
    flex-direction: column-reverse;
    justify-content: space-around;
    text-align: center;
  }
}

.box-content-right {
  margin-top: 24px;
  height: 100%;
  padding: 11px;
  opacity: 1;
  background: #7764E4 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #2C28281C;
  border: 1px solid #7764E4;
  border-radius: 5px;

  .text-content-right {
    letter-spacing: 0px;
    font-size: 12px !important;
    color: #ffff;
    opacity: 1;
    font-weight: bold;
    display: flex;
    align-content: stretch;
    flex-direction: column-reverse;
    justify-content: space-around;
    text-align: center;
  }

  .icon-modal {
    color: #CE858F;
    font-size: 110px;
  }
}


.content-left {
  margin-right: 20px !important;
  text-align: left;
  letter-spacing: 0px;
  color: #FFFFFF;
  opacity: 1;
  font-size: 25px !important;
  font-weight: bold !important;
}

.content-center {
  letter-spacing: 0px;
  color: #FFFFFF;
  opacity: 1;
  font-weight: bold !important;
  font-size: 11px !important;
  display: flex;
  align-items: center;
  margin-right: 10px !important;
}

.home-center {
  background: #FFFFFF 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #2C28281C;
  border-radius: 10px;
  opacity: 1;
  padding: 0px !important;

  .box-header {
    display: flex;
    align-items: center;
    .text-icon {
      color: #2E4E76;
      width: 30px;
      height: 30px;
      margin-top: -3px;
      margin-left: 20px;
    }
  }

  .text-header-home {
    text-align: left;
    font-weight: bold !important;
    font-size: 18px !important;
    letter-spacing: 0px;
    color: #2E4E76;
    opacity: 1;
    padding: 21px;
  }

  .text-head {
    color: #2E4E76;
    font: 500 16px/13px var(--font-family);
  }

  .content-head {
    display: flex;
  }

  .text-table {
    color: #2E4E76;
    font: 500 16px/13px var(--font-family);
    border-bottom: 0px;
    line-height: 19px;
    .action-text {
      cursor: pointer;
      background: #7764E4 0% 0% no-repeat padding-box;
      border-radius: 8px;
      opacity: 1;
      padding: 13px;
      color: #ffff;
      font: 500 16px/13px var(--font-family);
      line-height: 19px;
    }
  }
}

.content-information {
  padding-left: 0px !important;
  padding-right: 0px !important;
  display: flex !important;
  justify-content: space-between;
}

.box-information {
  background: #FFFFFF 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px rgba(44, 40, 40, 0.1098039216);
  border-radius: 10px;
  opacity: 1;
  padding-left: 0px !important;
  padding-right: 0px !important;
  width: 48%;

  .text-information {
    padding: 20px;
    letter-spacing: 0px;
    color: #172B4D;
    opacity: 1;
    font-size: 18px !important;
    font-weight: bold !important;
    padding-left: 34px;
  }

  .box-content-info {
    background: #F7FAFC 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 6px #2C28281C;
    border-radius: 10px;
    opacity: 1;
    padding: 20px;
    width: 90%;

    .text-content {
      font-weight: medium !important;
      font-size: 14px !important;
      letter-spacing: 0px;
      color: #172B4D;
      opacity: 1;
    }
  }

  .paper-information {
    background: #FFFFFF 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 6px #2C28281C;
    border-radius: 10px;
    opacity: 1;
  }
}

.box-icon {
  background: #FFFFFF 0% 0% no-repeat padding-box;
  box-shadow: 0px 0px 8px #00000029;
  opacity: 1;
  width: 13%;
  border-radius: 50%;
  padding-top: 20px;
  padding-bottom: 20px;
}
