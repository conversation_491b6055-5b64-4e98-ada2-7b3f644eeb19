$main_color: #2E4E76;
$color_white: #FFFFFF;
$title_color: #3F7EAE;
$footer_height: 60px;
$header_height: 70px;
$indent_page: 80px;
$tryit_to_bottom: 30px;
$tryit_box_with: 80px;

.box-main-content {
  height: 70vh;
  width: 100%;
  background-color: $main_color;
  position: relative;
  padding-top: 60px;

  .box-image {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 100%;
  }

  .box-image-cover {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 50%;
  }
}

.box-banner {
  padding-left: $indent_page;
  height: 100%;
  display: flex;
  align-items: flex-end;

  .title-header {
    font: 60px/32px var(--font-family);
    color: $color_white;
    text-shadow: 0px 0px 8px #00000052;
  }

  .box-banner-cover {
    z-index: 1;
  }

  .title-content {
    font: 16px/32px var(--font-family);
    color: #97CBE2;
    margin-top: 50px;
    width: 45%;
  }

  .box-footer {
    display: flex;
    width: 40%;
    margin-bottom: 40px;
    margin-top: 50px;
    justify-content: space-between;
    align-items: flex-end;

    .text-footer {
      font: 16px/29px var(--font-family);
      color: $color_white;
    }
  }
}

.box-try-it {
  width: $tryit_box_with;
  bottom: $tryit_to_bottom;
  left: calc(50% - $tryit_box_with);
  z-index: 999;
}

.title-footer,
.title-footer-tryit {
  font: bold 16px/24px var(--font-family);
  color: $main_color;
  text-decoration: none;
  display: flex;
  align-items: center;
  border-bottom: 2px solid $main_color;
  padding-bottom: 6px;
  cursor: pointer;

  .icon-north-east,
  .icon-east {
    font: 500 20px/24px var(--font-family);
    margin-left: 16px;
  }

  .icon-north-east {
    color: white;
    transform: matrix(0.71, -0.71, 0.71, 0.71, 0, 0);
  }
}

.title-footer-tryit {
  color: white;
  border-bottom: 1px solid white;
}

.box-content {
  height: 580px;
  background: #E6E6EF 0% 0% no-repeat padding-box;

  .cover-box {
    display: flex;
    justify-content: space-between;
    padding: $indent_page;
  }

  .box-wrap {
    width: 49%;
  }

  .box-left {
    .title-header-left {
      text-align: left;
      font: bold 24px/48px 'var(--font-family)',
      sans-serif;
      color: $main_color;
    }

    .title-content-left {
      font: 500 16px/24px var(--font-family);
      color: $main_color;
    }

    .box-footer-left {
      display: flex;
      margin-top: 40px;
    }
  }

  .box-right {
    .box-news-release-notes {
      display: flex;
      justify-content: space-between;
      border-top: 1px solid #8F8F9F;
      padding-top: 20px;
      padding-bottom: 40px;
    }

    .text-left {
      font: normal normal 600 24px/30px 'Montserrat', sans-serif;
      letter-spacing: 2.4px;
      color: $main_color;
    }

    .text-right {
      font: normal normal 600 24px/30px 'Montserrat', sans-serif;
      color: $title_color;
      letter-spacing: 2.4px;
      cursor: pointer;
      text-decoration: underline;
    }
  }

  .box-bottom {
    padding-bottom: 30px;

    .detail-link {
      display: flex;
      justify-content: space-between;
      text-decoration: none;
    }

    .text-content {
      font: 20px/30px var(--font-family);
      color: $title_color;
    }
  }

  .text-view {
    font: normal normal 600 20px/30px 'Montserrat', sans-serif;
    color: $title_color;
    text-align: end;
    cursor: pointer;
  }
}

.long-text {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  padding-right: 30px;
}

.w-30 {
  width: 30%;
}

@media only screen and (max-width: 1440px) {
  .box-main-content {
    height: 85vh;
  }
}
