.box-button-choose {
  width: 100%;
  height: 100%;
  padding: 20px 20px 16px 20px;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000052;
  border-radius: 10px;
  opacity: 1;

  .icon-choose {
    height: 100px;
    width: 100%;
    color: #2e4e76;
  }

  .button-choose {
    background: #e6e6ef 0% 0% no-repeat padding-box;
    box-shadow: 0px 0px 8px #00000029;
    border-radius: 4px;
    opacity: 1;
    width: 50%;
    letter-spacing: 0px;
    color: #3f7eae;
    text-align: center;
    opacity: 1;
    margin-left: 82px;
  }

  .text-button-content {
    text-align: left;
    font: normal normal 500 12px/18px var(--font-family) !important;
    letter-spacing: 0px;
    color: #4d4f5c;
    opacity: 1;
    margin-right: 20px;
  }

  .text-button-right {
    text-align: left;
    font: normal normal 13px/20px "Gotham", sans-serif !important;
    letter-spacing: 0px;
    color: #2dce98;
    opacity: 1;
  }

  .box-head {
    display: flex;
    justify-content: space-between;
  }

  .text-button-choose {
    text-align: left;
    font: normal normal bold 12px/12px var(--font-family), sans-serif !important;
    letter-spacing: 0px;
    color: #8898aa;
    opacity: 1;
  }

  .title-button {
    text-align: left;
    font: normal normal bold 18px/20px var(--font-family), sans-serif !important;
    letter-spacing: 0px;
    color: #4d4f5c;
    opacity: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.box-content {
  margin-top: 12px;
  height: 100%;
  padding: 12px 20.5px 12px 20.5px;
  opacity: 1;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #2c28281c;
  border: 1px solid #7764e4;
  border-radius: 5px;

  .text-content {
    letter-spacing: 0px;
    font: normal normal bold 13px/20px var(--font-family), sans-serif !important;
    color: #7764e4;
    opacity: 1;
    font-weight: bold;
    display: flex;
    align-content: stretch;
    flex-direction: column-reverse;
    justify-content: space-around;
    text-align: center;
  }
}

.box-content-right {
  margin-top: 14px;
  height: 100%;
  padding: 11px;
  opacity: 1;
  background: #7764e4 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #2c28281c;
  border: 1px solid #7764e4;
  border-radius: 5px;

  .text-content-right {
    letter-spacing: 0px;
    font-size: 12px !important;
    color: #ffff;
    opacity: 1;
    font-weight: bold;
    display: flex;
    align-content: stretch;
    flex-direction: column-reverse;
    justify-content: space-around;
    text-align: center;
    cursor: pointer;
  }

  .icon-modal {
    color: #ce858f;
    font-size: 110px;
  }
}
.content-left {
  margin-right: 47px !important;
  text-align: left;
  letter-spacing: 0px;
  color: #ffffff;
  opacity: 1;
  font: normal normal bold 25px/27px "Gotham", sans-serif !important;
}

.content-center {
  letter-spacing: 0px;
  color: #ffffff;
  opacity: 1;
  font: normal normal bold 11px/13px "Gotham", sans-serif !important;
  display: flex;
  align-items: center;
  margin-right: 10px !important;
}

.home-center {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #2c28281c;
  border-radius: 10px;
  opacity: 1;
  padding-left: 0px !important;
  padding-right: 0px !important;

  .text-header-home {
    text-align: left;
    font-weight: bold !important;
    font-size: 18px !important;
    letter-spacing: 0px;
    color: #172b4d;
    opacity: 1;
    padding: 21px;
  }

  .text-head {
    letter-spacing: 0px;
    color: #8898aa;
    opacity: 1;
    font-weight: bold;
    font-size: 12px;
  }

  .content-head {
    display: flex;
    justify-content: center;
  }

  .text-table {
    letter-spacing: 0px;
    color: #172b4d;
    opacity: 1;
    font-size: 14px;
    font-weight: bold;
    border-bottom: 0px;
  }
}

.content-information {
  display: grid !important;
  grid-template-columns: repeat(2, 1fr);
  column-gap: 20px;
  row-gap: 54px;
}

.box-information {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px rgba(44, 40, 40, 0.1098039216);
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .box-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    margin-bottom: 6px;

    .header-title {
      color: #172b4d;
      font-size: 18px;
      font-weight: bold;
    }
  }

  .box-footer {
    padding: 20px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  .text-information {
    letter-spacing: 0px;
    color: #172b4d;
    font: normal normal 500 14px/20px var(--font-family);
    opacity: 1;
  }

  .text-header-information {
    letter-spacing: 0px;
    color: #172b4d;
    font: normal normal bold 18px/20px var(--font-family), sans-serif !important;
    opacity: 1;
  }

  .text-info {
    font: bold 16px/24px var(--font-family), sans-serif;
    color: #172b4d;
  }

  .box-content-info {
    background: #f7fafc 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 6px #2c28281c;
    border-radius: 10px;
    opacity: 1;
    padding: 20px;
    width: 90%;
    font: normal normal 500 12px/21px var(--font-family);
    cursor: pointer;
    color: #172B4D;
    text-decoration: none;

    .text-content {
      font: normal normal 500 14px/21px var(--font-family);
      letter-spacing: 0px;
      color: #172b4d;
      opacity: 1;
    }
  }

  .paper-information {
    background: #ffffff 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 6px #2c28281c;
    border-radius: 10px;
    opacity: 1;

    .text-select {
      fieldset {
        border-color: rgba(0, 0, 0, 0);
      }
    }
  }
}

.box-icon {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 0px 8px #00000029;
  opacity: 1;
  width: 71px;
  height: 75px;
  border-radius: 50%;
  color: #2e4e76;
  display: flex;
  align-items: center;
  justify-content: center;
}

.select-tourist {
  border-radius: 50px !important;
  width: 108px;
  height: 26px;
  font: normal normal bold 12px/20px var(--font-family);
  letter-spacing: 0px !important;
  color: #465672 !important;
  opacity: 1;
  font-size: 12px !important;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 0px 4px #00000029;
}

.text-chart {
  text-align: left;
  font: normal normal medium 10px/20px var(--font-family) !important;
  letter-spacing: 0px !important;
  color: #bbbbc7 !important;
  opacity: 1;
  font-size: 10px !important;
  margin: 6px 0 !important;
}

.chart-label {
  display: flex;
  align-items: center;
  max-width: 100px;
  overflow: hidden;

  .label {
    font-size: 12px;
    font-weight: 500;
    margin-left: 6px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 80px;
  }
}

.circle {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #7664e4;
}

.button-import {
  font: bold 13px/20px var(--font-family) !important;
  color: #7764e4 !important;
  border: 1px solid #7764e4 !important;
  margin-top: 28px !important;
  padding: 12px 10.5px !important;
  background-color: #ffffff !important;
}
.link-qa {
  text-align: right;
  color: #7764e4;
  border-bottom: solid 1px #7764e4;
  font: normal normal 500 14px/12px var(--font-family) !important;
  display: inline-block;
}

.div-link-qa {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-right: 34px;
}

.text-qa {
  margin-left: 20px !important;
  color: #2e4e76 !important;
  font: normal normal bold 16px/13px var(--font-family), sans-serif !important;
  text-decoration: none;
}

.box-option {
  background: #f7fafc;
  box-shadow: 0px 0px 4px #00000029;
  list-style: none;
  padding: 20px 0 0;
  margin-top: -10px;
  z-index: 1;
  margin-bottom: 20px;

  .text-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font: normal normal 500 14px/21px var(--font-family) !important;
    padding: 10px 20px;

    &:nth-child(even) {
      background-color: white;
    }

    &-article {
      color: #7764E4;
      text-decoration: none;
    }

    &-name {
      color: #172B4D;
    }
  }
}

.report-item {
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  flex-grow: 1;

  &-title {
    font-weight: bold;
    color: #2e4e76;
    font-size: 12px;

    &--span {
      font-size: 10px;
      color: #3f7eae;
      margin-left: 12px;
    }
  }

  &-subtitle {
    font-size: 10px;
    color: #172b4d;
  }
}

.report-table {
  width: 100%;
  border-collapse: collapse;

  th {
    font-weight: bold;
    text-align: right;

    &:first-child {
      color: #3f7eae;
      text-align: start;
      width: 55%;
    }

    &:nth-child(2) {
      color: #7764e4;
    }

    &:nth-child(3) {
      color: #ce858f;
    }
  }

  td {
    font-weight: 500;

    &:first-child {
      width: 55%;
      max-width: 55%;
    }

    p {
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      margin: 0;
    }

    &:not(:first-child) {
      text-align: right;
    }
  }

  th,
  td {
    color: #2e4e76;
  }

  th {
    font-size: 10px;
    font-weight: 500;
  }

  td {
    font-size: 14px;
  }

  tbody {
    background-color: #f8f9fd;
    td,
    th {
      background-color: #f8f9fd;
    }
  }

  .detail-row {
    td:first-child {
      padding-top: 12px;
    }
  }
}
