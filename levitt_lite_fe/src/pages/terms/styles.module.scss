$main_color: #2E4E76;
$color_white: #FFFFFF;
$title-color: #3F7EAE;
$footer_height: 60px;
$header_height: 70px;


.box-main-content {
  height: 1340px;
  width: 100%;
  background: #F1F3F9 0% 0% no-repeat padding-box;
  .box-title {
    display: flex;
    justify-content: center;
  }
  .title-header {
    font: normal normal bold 24px/30px var(--font-family);
    letter-spacing: 3.2px;
    color: $main_color;
    margin-top: 120px;
    line-height: 1.25;
    width: 90%;
  }
}

.box-news {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;

  .title-date {
    font: bold 16px/30px var(--font-family);
    letter-spacing: 3.2px;
    color: $main_color;
    border-top: 1px solid $title-color;
    margin-top: 35px;
    padding-top: 20px;
  }
  .box-date {
    width: 90%;
  }
  .box {
    width: 720px;
  }
  .title-content {
    font: normal normal 300 16px/30px var(--font-family);
    letter-spacing: 1.6px;
    color: #1A1B35;
    opacity: 1;
    margin-top: 45px;
    padding-top: 20px;
    text-align: justify;
  }
  .title-box-header {
    font: normal normal bold 20px/30px var(--font-family);
    letter-spacing: 2px;
    color: #1A1B35;
    opacity: 1;
    margin-top: 40px;
    text-align: justify;
  }
  .title-view {
    font: normal normal 600 20px/30px 'Montserrat', sans-serif;
    letter-spacing: 0px;
    color: #3F7EAE;
    border-top: 1px solid $title-color;
    margin-top: 55px;
    padding-top: 20px;
    text-align: end;
    cursor: pointer;
  }
  .box-view {
    width: 90%;
  }
}
