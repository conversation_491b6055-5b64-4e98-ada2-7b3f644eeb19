code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

@font-face {
  font-family: 'SourceHanSansJP';
  src: url('./assets/fonts/SourceHanSansJP-Bold.otf') format('opentype');
  font-weight: 700;
  font-display: swap;
}

@font-face {
  font-family: 'SourceHanSansJP';
  src: url('./assets/fonts/SourceHanSansJP-Light.otf') format('opentype');
  font-weight: 300;
  font-display: swap;
}

@font-face {
  font-family: 'SourceHanSansJP';
  src: url('./assets/fonts/SourceHanSansJP-Medium.otf') format('opentype');
  font-weight: 500;
  font-display: swap;
}

@font-face {
  font-family: 'SourceHanSansJP';
  src: url('./assets/fonts/SourceHanSansJP-Regular.otf') format('opentype');
  font-weight: 400;
  font-display: swap;
}

/* Import font Montserrat */
@import url('https://fonts.googleapis.com/css?family=Montserrat:regular,bold,italic&subset=latin,latin-ext');
@import url("./Icons.scss");

@font-face {
  font-family: 'Gotham';
  src: url('assets/Gotham-Font/Gotham-Bold.otf');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

body {
  margin: 0;
  font-family: 'SourceHanSansJP',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    'Roboto',
    'Oxygen',
    'Ubuntu',
    'Cantarell',
    'Fira Sans',
    'Droid Sans',
    'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  --font-family: 'SourceHanSansJP';
}

/* width */
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #DBDBDB;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #BCBCBC;
  border-radius: 10px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #2e4e76;
}

.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.custom-editor .ck.ck-editor__main>.ck-editor__editable:not(.ck-focused) {
  height: 642px;
}

.custom-editor .ck.ck-editor__editable.ck-focused:not(.ck-editor__nested-editable) {
  height: 642px;
}

.react-datepicker-popper {
  z-index: 100 !important;
}

.clickable {
  cursor: pointer;
}

.table-body {
  tr {
    td {
      color: #2E4E76;
      font-weight: bold;
      font-size: 12px;
      border: 0;

      &.group-name {
        color: #7764E4;
        border-right: 2px solid #8F8F9F;
      }
    }
  }
}

.even-row {
  tr:nth-child(even) {
      background: #F8F8F8 0% 0% no-repeat padding-box;
    }
}

.custom-datepicker-container {
  .react-datepicker-wrapper {
    width: 100%;
  }
}

.custom-datepicker-confirm-container {
  .react-datepicker-wrapper {
    max-height: 47px;
  }
}

.custom-datepicker {
  &.react-datepicker {
    border: 0;
    box-shadow: 0px 2px 8px #00000052;
    border-radius: 0;
    padding: 14px 20px;
  }

  .react-datepicker__header {
    background-color: transparent;
  }

  .react-datepicker__navigation {
    top: 20px;
  }

  .react-datepicker__triangle {
    display: none;
  }

  .react-datepicker__day-name,
  .react-datepicker__day,
  .react-datepicker__time-name,
  .react-datepicker__current-month,
  .react-datepicker__month-text,
  .react-datepicker__header {
    color: #2E4E76;
    font-weight: bold;

    &.react-datepicker__day--disabled {
      opacity: .5;
    }
  }

  .react-datepicker__day--selected,
  .react-datepicker__day--in-selecting-range,
  .react-datepicker__day--in-range,
  .react-datepicker__month-text--selected,
  .react-datepicker__month-text--in-selecting-range,
  .react-datepicker__month-text--in-range,
  .react-datepicker__quarter-text--selected,
  .react-datepicker__quarter-text--in-selecting-range,
  .react-datepicker__quarter-text--in-range,
  .react-datepicker__year-text--selected,
  .react-datepicker__year-text--in-selecting-range,
  .react-datepicker__year-text--in-range,
  .react-datepicker__month-text--selected {
    background-color: #E6E6EF;
    transition: .24s;

    &:hover {
      background-color: #a7a7ac;
    }
  }
}

.micado-table {
  .table-head {
    box-shadow: 0px 1px 4px #00000029;

    .header-cell {
      color: #8898AA;
      font-size: 12px;
      font-weight: bold;
      line-height: 1;
    }

    th {
      background-color: #F1F3F9;
      padding: 12px;
      line-height: 1;

      &[aria-sort] {
        .header-cell {
          color: #3F7EAE;
        }
      }
    }
  }

  .table-body-row {
    cursor: pointer;

    &[aria-checked="true"] {
      background-color: #97CBE2;

      &:hover {
        background-color: #a9d0e1;
      }
    }


  }

  .table-body-row--add-group {
    td {
      color: #7764E4;
    }
  }
}

.errors-date {
  border: 1px solid #d32f2f;
  border-radius: 4px;
}

.error-text {
  color: #d32f2f;
  font-size: 0.75rem;
}

@keyframes width-increase {
  0% {
    width: "0%",
  }

  100% {
    width: var(--width, 0%),
  }
}


.edit-plan-group-popover-container {
  color: #2E4E76;
  display: flex;
  flex-direction: column;
  border-radius: 8px;

  .popover--text {
    font-weight: 500 !important;
    font-size: 14px;
  }


.text-field {
  color: #2E4E76;
  margin: 2px 2px 12px 2px !important;
  border-color: #7764E4;
  width: 100%;

  div {
    color: currentColor;
  }

  input {
    padding: 16px;
    font-size: 12px;
    font-weight: 500;
  }

  fieldset {
    border-color: #7764E4 !important;
  }

  p {
    margin-left: 0px;
  }

  &.new-group-input {
    margin-bottom: 0 !important;

    input {
      padding: 8px;
    }
  }
}
}


.text-nowrap {
  white-space: nowrap;
}

.reportContainer {
  padding: 16px;

  p {
    font-size: 14px !important;
    font-weight: 500;
    color: #2E4E76;
    padding: 0 32px;
  }
}
