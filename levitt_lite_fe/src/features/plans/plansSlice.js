import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import HTTP from "../../http_common";
import { UNREGISTER } from "../../constants";

const initialState = {
  list: [],
  listData: null, // Store complete API response including totals
  loading: false,
  paginate: {
    count: 0,
    next: null,
    previous: null,
    total_pages: 0,
  },
  error: null,
  planResult: null,
  planRequested: null,
  handlingPlans: {
    header: [],
    ids: [],
  },
};

export const getListPlan = createAsyncThunk(
  "plans/getListPlan",
  async (body) => {
    const response = await HTTP.get(`/api/plans/`, {
      params: { ...body },
    });
    return response;
  }
);

export const createPlan = createAsyncThunk("plans/createPlan", async (body, { rejectWithValue }) => {
  try {
    const response = await HTTP.post(`/api/gpt_plans/create/asy/`, body);
    return response;
  } catch (error) {
    // Handle different types of errors
    if (error.response) {
      // Server responded with error status
      const statusCode = error.response.status;

      if (statusCode === 429) {
        return rejectWithValue({
          statusCode: 429,
          message: "API呼び出し制限に達しました。しばらくしてから再試行してください。"
        });
      } else if (statusCode === 400) {
        return rejectWithValue({
          statusCode: 400,
          message: "リクエストデータが無効です。入力内容を確認してください。"
        });
      } else if (statusCode === 401) {
        return rejectWithValue({
          statusCode: 401,
          message: "認証に失敗しました。再度ログインしてください。"
        });
      } else if (statusCode === 500) {
        return rejectWithValue({
          statusCode: 500,
          message: "サーバーエラーが発生しました。再試行してください。"
        });
      } else {
        return rejectWithValue({
          statusCode: statusCode,
          message: "サーバーエラーが発生しました。再試行してください。"
        });
      }
    } else if (error.request) {
      // Network error
      return rejectWithValue({
        statusCode: 0,
        message: "ネットワークエラーです。接続を確認してください。"
      });
    } else {
      // Other errors
      return rejectWithValue({
        statusCode: 0,
        message: "予期しないエラーが発生しました。"
      });
    }
  }
});

export const getGPTPlanAsync = createAsyncThunk("plans/getGPTPlanAsync", async (id) => {
  try {
    const response = await HTTP.get("/api/gpt_plans/get/" + id);
    return response;
  } catch (error) {
    throw error;
  }
});

export const importPlan = createAsyncThunk("plans/import", async (body) => {
  const formData = new FormData();
  formData.append("csv_file", body.file);
  body.headers.forEach((header, index) => {
    formData.append(`headers[${index}]key`, header.key);
    formData.append(`headers[${index}]custom`, header.custom);
  });
  const response = await HTTP.post(`/api/plans/import`, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response;
});

export const customHeaderImportPlan = createAsyncThunk(
  "plans/customHeaderImportPlan", async (file) => {
    const formData = new FormData();
    formData.append("csv_file", file);
    const response = await HTTP.post(`/api/plans/header-import`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response;
  });

export const getListPlanWithIds = createAsyncThunk(
  "plans/getListPlanWithIds",
  async (ids) => {
    const response = await HTTP.post("/api/plans/create-multiple", {
      ids: ids,
    });
    return response;
  }
);

export const updateMultiPlan = createAsyncThunk(
  "plans/updateMultiPlan",
  async (data) => {
    const response = await HTTP.post(`api/plans/update-multiple/`, data);
    return response;
  }
);

export const deleteMultiPlans = createAsyncThunk(
  "plans/deleteMultiPlan",
  async (ids) => {
    const response = await HTTP.delete(
      "/api/plans/create-multiple?ids=" + ids.join(",")
    );
    return response;
  }
);

export const changePlansGroup = createAsyncThunk(
  "plans/changePlansGroup",
  async ({ groupId, planIds }) => {
    const response = await HTTP.put(`api/group_plans/${groupId}/update/`, {
      plan_ids: planIds,
    });
    return response;
  }
);

export const plansSlice = createSlice({
  name: "plans",
  initialState,
  reducers: {
    setPlanRequested: (state, { payload }) => {
      state.planRequested = payload;
    },
  },
  extraReducers: {
    [getListPlan.pending]: (state) => {
      state.loading = true;
      state.error = null;
    },
    [getListPlan.fulfilled]: (state, { payload }) => {
      state.loading = false;
      const { results, ...paginate } = payload;

      // Store the complete results data including totals
      state.listData = results;

      state.list = results?.data.map((plan) => {
        if (plan) {
          return {
            ...plan,
            sale_period:
              plan?.sale_period_start && plan?.sale_period_end
                ? `${plan?.sale_period_start}~${plan?.sale_period_end}`
                : UNREGISTER,
            accommodation_period:
              plan?.accommodation_period_start && plan?.accommodation_period_end
                ? `${plan?.accommodation_period_start}~${plan?.accommodation_period_end}`
                : UNREGISTER,
          };
        }
        return plan;
      });
      state.paginate = paginate;
    },
    [getListPlan.rejected]: (state, { error }) => {
      state.loading = false;
      state.error = error.message;
    },

    [createPlan.pending]: (state) => {
      state.loading = true;
      state.error = null;
    },
    [createPlan.fulfilled]: (state, { payload }) => {
      state.loading = false;
      if (payload.error) {
        state.error = payload.error;
      }
    },
    [createPlan.rejected]: (state, { payload, error }) => {
      state.loading = false;
      // Use payload from rejectWithValue if available, otherwise use error
      if (payload) {
        state.error = payload.message || payload;
      } else {
        state.error = error.message || "An error occurred";
      }
    },

    [getGPTPlanAsync.pending]: (state) => {
      state.loading = true;
      state.error = null;
    },
    [getGPTPlanAsync.fulfilled]: (state, { payload }) => {
      state.loading = false;
      if (payload.error) {
        state.error = payload.error;
      } else if (payload.status !== 'pending') {
        state.planResult = payload.data;
      }
    },
    [getGPTPlanAsync.rejected]: (state, { error }) => {
      state.loading = false;
      state.error = error;
    },

    [importPlan.pending]: (state) => {
      state.loading = true;
      state.error = null;
    },
    [importPlan.fulfilled]: (state, { payload }) => {
      state.loading = false;
      state.list = payload?.results?.data;
    },
    [importPlan.rejected]: (state, { error }) => {
      state.loading = false;
      state.error = error.message;
    },

    [getListPlanWithIds.pending]: (state) => {
      state.loading = true;
      state.error = null;
    },
    [getListPlanWithIds.fulfilled]: (state, { payload }) => {
      state.loading = false;
      state.list = payload?.results?.data;
    },
    [getListPlanWithIds.rejected]: (state, { error }) => {
      state.loading = false;
      state.error = error.message;
    },

    [updateMultiPlan.pending]: (state) => {
      state.loading = true;
      state.error = null;
    },
    [updateMultiPlan.fulfilled]: (state, { payload }) => {
      state.loading = false;
      state.list = payload?.results?.data;
    },
    [updateMultiPlan.rejected]: (state, { error }) => {
      state.loading = false;
      state.error = error.message;
    },
  },
});
export const { setPlanRequested } = plansSlice.actions;
export default plansSlice.reducer;
