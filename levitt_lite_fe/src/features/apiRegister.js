import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

const { REACT_APP_API_ENDPOINT } = process.env;
export const register = createAsyncThunk('register/fetchRegister', async (body) => {
  const formData = new FormData();
  formData.append('email', body.email);
  if(body.role_type) { formData.append('is_superuser', body.role_type) }

  const response = await fetch(`${REACT_APP_API_ENDPOINT}/api/users/register`, {
    method: 'POST',
    body: formData,
  });
  const jsonData = await response.json();
  return jsonData;
});

const apiSlice = createSlice({
  name: 'register',
  initialState: {
    data: null,
    loading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(register.pending, (state) => {
        state.loading = true;
      })
      .addCase(register.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload;
      })
      .addCase(register.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;

      });
  },
});

export default apiSlice.reducer;
