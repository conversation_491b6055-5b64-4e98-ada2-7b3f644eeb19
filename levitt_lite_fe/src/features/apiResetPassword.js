import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

const { REACT_APP_API_ENDPOINT } = process.env;
export const resetPassword = createAsyncThunk('resetPassword/fetchResetPassword', async (body) => {
  const formData = new FormData();
  formData.append('password', body.password);

  const response = await fetch(`${REACT_APP_API_ENDPOINT}/api/password-reset/${body.uid}/${body.token}/`, {
    method: 'POST',
    body: formData,
  });
  const jsonData = await response.json();
  return jsonData;
});

const apiSlice = createSlice({
  name: 'register',
  initialState: {
    data: null,
    loading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(resetPassword.pending, (state) => {
        state.loading = true;
      })
      .addCase(resetPassword.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload;
      })
      .addCase(resetPassword.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      });
  },
});

export default apiSlice.reducer;
