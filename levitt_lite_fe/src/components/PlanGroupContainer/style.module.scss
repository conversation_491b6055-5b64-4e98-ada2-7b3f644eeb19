
.plan-group-header {
  color: #2E4E76;

  .box-date {
    border: 1px solid #97CBE2;
    border-radius: 4px;
  }

  .plan-group-title {
    font-size: 18px;
    font-weight: bold;
    font: bold 18px/21px var(--font-family) !important;
    color: #172B4D;
  }

  &--selected-rows {
    background-color: #2E4E76;
    width: 100%;
    padding: 16px 0;
    color: #fff;

    .header--text {
      font-size: 14px;
      font-weight: 500;
      padding: 10px 20px;

      &.first-text {
        border-right: 1px solid currentColor;
        margin-right: 12px;
        padding: 10px 30px 10px 12px;
      }
    }

    .deselected-rows {
      margin-right: 30px;
      cursor: pointer;

      &--text {
        font-size: 12px;
        font-weight: bold;
      }
    }
  }
}

.group-buttons {
  color: #2E4E76;

  .background {
    background-color: #E6E6EF !important;
  }

  .button-text {
    cursor: pointer;
    text-align: center;
    min-width: 84px;
    padding: 12px 18px;
    background: transparent;
    border: none;
    color: #2E4E76;

    &.active {
      color: #7764E4;
    }

    &:hover {
      background-color: #E6E6EF;
    }

    p {
      font-size: 12px;
      font-weight: bold;
    }
  }
}

.table-footer-row {
  td {
    background-color: #E6E6EF;
    font-weight: bold;
    color: #2E4E76;
  }
}

.dropdown-menu {
  z-index: 100;
  background-color: #FFFFFF;
  border-radius: 4px;
  box-shadow: 0px 0px 8px #00000052;
}

.text-field {
  color: #2E4E76;
  margin: 2px 2px 12px 2px !important;
  border-color: #7764E4;
  width: 100%;

  div {
    color: currentColor;
  }

  input {
    padding: 16px;
    font-size: 12px;
    font-weight: 500;
  }

  fieldset {
    border-color: #7764E4 !important;
  }

  p {
    margin-left: 0px;
  }

  &.new-group-input {
    margin-bottom: 0 !important;

    input {
      padding: 8px;
    }
  }
}
.box-date-picker {
  fieldset {
    border: 1px solid #97CBE2 !important;
    border-radius: 10px;
  }
}

.custom-datepicker-button {
  min-width: 139px;
  padding: 4px 12px;
  color: #172B4D;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  background-color: #fff;
  outline: none;
  border: 0;
  text-align: start;
  min-height: 32px;
  display: flex;
  align-items: center;
  border: 1px solid #97CBE2;
  cursor: pointer;
  transition: .24;
  font: bold 18px/21px var(--font-family) !important;

  &:hover {
    background-color: #97CBE2;
  }

  .icon-choose {
    margin-left: 12px;
    margin-top: 2px;
    color: #8F8F9F;
  }
}

.box-date {
  height: 34px;
  background: #F7F7F8 0% 0% no-repeat padding-box;
  border: 0px solid #ced4da;
  font: 500 16px/13px var(--font-family);
  color: #2E4E76;
  padding: 13px;
  max-width: 100%;
}

.popover-container {
  color: #2E4E76;
  display: flex;
  flex-direction: column;
  border-radius: 8px;

  .popover--text {
    font-weight: 500 !important;
    font-size: 14px;
  }
}

.plan-group-list {
  max-height: 130px;
  overflow-y: auto;

  &--item {
    display: flex;
    justify-content: space-between;
    color: #2E4E76;
    align-items: center;

    &:nth-child(even) {
      background-color: #F7FAFC;
    }

    // &:hover {
    //   background-color: #97CBE2;
    // }

    &-title {
      font-size: 12px;
      font-weight: 500;
      flex-grow: 1;
      text-align: start;
      padding: 0 20px;
      width: 300px;
      max-width: 300px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    &-edit {
      color: #8F8F9F;
      flex-grow: 1;
      text-align: start;
      padding: 10px;
      cursor: pointer;
    }
  }

  &--footer {
    padding: 12px;
    box-shadow: 0px -2px 3px #00000029;
  }
}

.download-csv {
  a {
    text-decoration: none;
    color: #2e4e76;
  }
}
