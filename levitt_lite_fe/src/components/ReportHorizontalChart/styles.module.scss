.chart-container {
  width: 100%;
}

.line {
  width: 100%;
  border-bottom: 1px solid #B7E1F1;
  background-color: transparent;
}

.grouped-line {
  height: 3px;
  display: flex;
}

.chart-data {
  border-radius: 10px;
  height: 0;
  border-width: 2px;
  border-style: solid;
  position: relative;
  z-index: 1;
  width: 0;

  .grouped-detail {
    position: absolute;
    right: 0;
    bottom: 0;
    background-color: #fff;
    display: none;
    padding: 8px 12px;
    border-radius: 4px;
    box-shadow: 0px 1px 8px #00000052;
    z-index: 10;

    .grouped-detail-title,
    .grouped-detail-value {
      white-space: nowrap;
      margin: 0;
      font-size: 12px;
      line-height: 1;
    }

    .grouped-detail-title {
      color: #172B4D;
      font-weight: bold;
    }

    .grouped-detail-value {
      color: #2E4E76;
    }
  }

  &:hover {
    transform: scaleY(1.3);
    z-index: 2;

    .grouped-detail {
      display: block;
      transform: scaleY(.7) translate(110%, 50%);
    }
  }
}
