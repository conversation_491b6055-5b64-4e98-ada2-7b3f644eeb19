.text-sidebar {
  margin: 0 12px 0 0!important;
  text-align: left;
  font-weight: bold !important;
  font-size: 25px !important;
  letter-spacing: 0px !important;
  color: #172b4d;
  opacity: 1;
}

.box-button {
  &:hover {
    background-color: rgba(0, 0, 0, 0) !important;
  }
  span {
    display: none;
  }
}

.text-label {
  margin-top: -6px;
  margin-left: 28px;
  font: normal normal 500 12px/19px var(--font-family) !important;
  color: #1A1B35 !important;
}

.box-rate {
  display: flex;
  align-items: center;
  background: #3F7EAE 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #1717172D;
  border-radius: 25px;
  opacity: 1;
  padding: 9px 8px 9px 28px;
  .text-title {
    font: normal normal bold 12px/19px var(--font-family) !important;
    color: #F0F0F7;
    margin-right: 8px;
  }
  .text-number {
    font: normal normal bold 16px/13px var(--font-family) !important;
    color: #F0F0F7;
    margin-right: 4px;
  }
  .text-number-right {
    font: normal normal bold 16px/13px var(--font-family) !important;
    color: #F0F0F7;
    margin-left: 4px;
    margin-top: 5px;
    margin-right: 16px;
  }
}

.card-input {
  margin-left: 21px !important;
  margin-right: 31px !important;
  div {
    height: 40px;
    background: #F7FAFC 0% 0% no-repeat padding-box;
    width: 348px;
    border-radius: 40px;
  }
  input {
    font: normal normal 500 16px/13px var(--font-family) !important;
    color: #1A1B35;
  }
}
