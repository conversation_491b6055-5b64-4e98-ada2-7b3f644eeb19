.table-head {
  box-shadow: 0px 1px 4px #00000029;

  .header-cell {
    color: #8898aa;
    font-size: 12px;
    font-weight: bold;
    line-height: 1;
  }

  th {
    background-color: #f1f3f9;
    min-height: 80px;
    padding: 12px;
    line-height: 1;

    &[aria-sort] {
      .header-cell {
        color: #3f7eae;
      }
    }
  }
}

.table-body-row {
  height: 80px;

  &[aria-checked="true"] {
    background-color: #97cbe2 !important;

    &:hover {
      background-color: #a9d0e1;
    }
  }
}

.table-body {
  tr:nth-child(even) {
    background: #f7fafc 0% 0% no-repeat padding-box;
  }

  tr {
    td {
      // padding: 32px 30px;
      border-bottom: 0px solid rgba(224, 224, 224, 1);
      font: normal normal 700 12px/13px var(--font-family) !important;
      p {
        font: normal normal 700 12px/13px var(--font-family) !important;
      }
    }
  }
}

.custom-table-container {
  display: flex;
  flex-grow: 1;
  max-height: 100%;
  overflow: auto;

  table {
    border-collapse: separate;

    thead tr th {
      position: sticky;
      top: 0;
    }

    tfoot tr {
      th, td {
        position: sticky;
        bottom: 0;
      }
    }
  }
}

.no-data {
  height: 100%;
  width: 100%;
  color: #8898aa;
  text-align: center;
  padding: 100px;

  .no-data-text {
    font-size: 16px;
    line-height: 100%;
  }
  .no-data-link {
    margin-top: 20px;

    a {
      color: #7764E4;
      font-size: 16px;
    }
  }
}
