.box-main {
  flex-grow: 1;
  height: 100vh;
  overflow: auto;
  background-color: #F1F3F9;
}

.home-center {
  background: #FFFFFF 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #2C28281C;
  border-radius: 10px;
  opacity: 1;
  padding-left: 0px !important;
  padding-right: 0px !important;
  margin-top: 100px;
  margin-bottom: 32px;
  max-width: 94%!important;

  .box-header {
      display: flex;
      align-items: center;
      border-bottom: 2px solid #2E4E76;
      justify-content: space-between;

      .text-icon {
          width: 30px;
          height: 30px;
          margin-top: -3px;
          margin-left: 20px;
          color: #2E4E76;
      }

      .text-create {
          background: #7764E4 0% 0% no-repeat padding-box;
          border-radius: 8px;
          opacity: 1;
          color: #FFFFFF;
          font: normal normal 500 16px/13px var(--font-family);
          padding: 16px;
          margin-right: 20px;
          cursor: pointer;
      }
  }

  .text-header-home {
      text-align: left;
      font-weight: bold !important;
      font-size: 18px !important;
      letter-spacing: 0px;
      color: #2E4E76;
      opacity: 1;
      padding: 21px;
  }

  .text-head {
      color: #2E4E76;
      font: normal normal bold 16px/13px var(--font-family);
  }

  .box-grid {
      margin-left: 0px;
      margin-top: 0px;
      height: 70px;
      align-items: center;
      border-bottom: 1px solid #B7E1F1;
      width: 100%;
  }

  .box-editor {
      margin-left: 0px;
      margin-top: 0px;
      height: 710px;
      align-items: center;
      width: 100%;

      .grid-item {
          margin-left: 18px;
      }
  }

  .grid-item {
      padding: 0px !important;
      .box-date {
          margin-left: 18px;
          width: 177px;
          height: 34px;
          background: #F7F7F8 0% 0% no-repeat padding-box;
          border: 0px solid #ced4da;
          font: normal normal 500 16px/13px var(--font-family);
          color: #2E4E76;
          padding: 13px;
      }
      .errors-date {
        border: 1px solid #d32f2f;
        border-radius: 4px;
      }
  }
  .error-date {
      margin-left: 32px;
      color: #d32f2f;
      font-size: 0.75rem;
  }

  .content-head {
      display: flex;
  }

  .text-left {
      color: #2E4E76;
      padding: 0px !important;
      font: normal normal bold 16px/13px var(--font-family);
      background: #F1F3F9;
      height: 68px;
      display: flex;
      padding-left: 20px !important;
      align-items: center;
      max-width: 12%;
      line-height: 19px;
  }

  .text-bottom {
      color: #2E4E76;
      font: normal normal bold 16px/13px var(--font-family);
      background: #F1F3F9;
      height: 710px;
      padding-left: 20px !important;
      max-width: 12%;
      padding-top: 20px !important;
  }

  .text-field {
      width: 896px;
      margin-left: 16px;

      input {
          height: 8px;
          color: #2E4E76;
          font: normal normal 500 16px/22px var(--font-family);
      }

      div {
          background: #F7F7F8 0% 0% no-repeat padding-box;
          border-radius: 4px;
      }

      fieldset {
          border-color: #FFFFFF;
      }
  }

  .text-select {
      width: 180px;
      background: #F7F7F8 0% 0% no-repeat padding-box;
      height: 40px;
      border-radius: 4px;
      margin-left: 16px;
      color: #2E4E76;
      font: normal normal 500 16px/22px var(--font-family);

      fieldset {
          border-color: #FFFFFF;
      }
  }

  .box-group {
      flex-direction: row;
      margin-top: 9px;

      .text-group {
          font: normal normal 500 16px/13px var(--font-family);
          letter-spacing: 0px;
          color: #2E4E76;
      }
  }
}
