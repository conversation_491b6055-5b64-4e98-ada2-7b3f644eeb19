.box-header {
  padding-bottom: 12px;
  width: 100%;
  border-bottom: 1px solid #97CBE2;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .icon-download {
    width: 28px;
    height: 28px;
    color: #2E4E76;
  }
}

.custom-datepicker-button {
  min-width: 160px;
  padding: 4px 12px;
  color: #2E4E76;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  background-color: #E6E6EF;
  outline: none;
  border: 0;
  text-align: start;
  min-height: 32px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: .24;

  &:hover {
    background-color: #97CBE2;
  }

  .icon-choose {
    margin-left: 12px;
    color: #8F8F9F;
  }
}

.report-container {
  display: grid;
  grid-template-columns: 70% auto;
  column-gap: 40px;

  .report-box {
    box-shadow: 0px 3px 6px #00000029;
    background: #FFFFFF;
  }
}

.report-box-header {
  color: #2E4E76;
  padding: 12px 32px;
  border-bottom: 1px solid #3F7EAE;
  display: flex;
  align-items: center;
  justify-content: space-between;

  p {
    margin: 0;
  }

  &-title {
    font-size: 16px;
    font-weight: bold;
  }

  &-date {
    color: #3F7EAE;
    font-size: 12px;
    font-weight: 500;
  }
}

.report-list {
  color: #172B4D;
  font-weight: 500;
}

.report-items {
  padding: 20px 32px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;

  .report-item {
    box-shadow: 0px 0px 4px #00000052;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;

    &-title {
      font-weight: bold;
      color: #2E4E76;
      font-size: 12px;
    }

    &-subtitle {
      font-size: 10px;
      color: #172B4D;
    }
  }
}

.report-table {
  width: 100%;
  border-collapse: collapse;

  th {
    font-weight: bold;
    text-align: right;

    &:first-child {
      color: #172B4D;
      text-align: start;
      width: 55%;
    }

    &:nth-child(2) {
      color: #7764E4;
    }

    &:nth-child(3) {
      color: #CE858F;
    }
  }

  td {
    font-weight: 500;

    &:not(:first-child) {
      text-align: right;
    }
  }

  th,
  td {
    font-size: 10px;
    color: #2E4E76;
  }

  tbody {
    background-color: #F8F9FD;
    td,
    th {
      background-color: #F8F9FD;
    }
  }

  .detail-row {
    td:first-child {
      padding-top: 12px;
    }
  }
}

.btn-navigate {
  border-radius: 50% !important;
  padding: 12px;
  min-width: unset !important;
  width: 40px !important;
  height: 40px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #2E4E76 !important;
  background-color: #B7E1F1 !important;

  &:hover {
    color: #B7E1F1;
  }
}

.report-menu-items {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 40px 0 30px 0;

  .selected {
    background-color: #F0F0F7;
    color: #3F7EAE;
  }
}

.report-box-footer {
  border-top: 1px solid #3F7EAE;
  padding: 16px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &-text {
    font-size: 12px;
    color: #2E4E76;
    font-weight: bold;
  }

  .logo {
    width: 120px;
    height: 20px;
    background-image: url("../../assets/images/logo.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
}

.price-ranges {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24px;

  .price-range {
    color: #2E4E76;
    font-size: 14px;
    font-weight: 500;
    line-height: 1;
    display: flex;
    align-items: center;

    &-symbol {
      margin-right: 12px;
    }

    &-symbol-end {
      margin-left: 12px;
    }

    &-start {
      margin-right: 8px;
      font-size: 20px;
    }

    &-currency {
      margin-right: 8px;
    }

    &-value {
      padding: 6px 32px 6px 12px;
      background-color: #F7F7F8;
      border-radius: 4px;
    }
  }
}

.calendar-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  height: 40px;
  max-height: 40px;

  .btn {
    padding: 10px 32px;
    background: transparent;
    border: none;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
    border-radius: 4px;
    transition: .24s;
    white-space: nowrap;

    &.btn-cancel {
      color: #465672;

      &:hover {
        background-color: #F0F0F7;
      }
    }

    &.btn-apply {
      background-color: #172B4E;
      color: white;

      &:hover {
        background-color: #2E4E76;
      }
    }
  }
}
