# Micado React JS Client

[![JavaScript](https://img.shields.io/static/v1?style=for-the-badge&message=JavaScript&color=222222&logo=JavaScript&logoColor=F7DF1E&label=)](https://developer.mozilla.org/en-US/docs/Web/JavaScript)
[![Node.js](https://img.shields.io/static/v1?style=for-the-badge&message=Node.js&color=339933&logo=Node.js&logoColor=FFFFFF&label=)](https://nodejs.org/en)
[![React](https://img.shields.io/static/v1?style=for-the-badge&message=React&color=222222&logo=React&logoColor=61DAFB&label=)](https://react.dev/)

Micadoプロジェクトのサーバーバックエンド。ReactJSフレームワークを使用してプランを作成するAPI。

## システム要件

macOSまたはLinux

- Node JS: [NodeJSのインストールガイド](https://nodejs.org/en)

## 開発環境

使用する技術

- **nodejs:** v18.17.1
- **npm:** v9.8.0

## インストール手順

1. プロジェクトのクローン

```bash
<NAME_EMAIL>:HapoDivOne/micado-web-fe.git
cd micado-web-fe
```

2. Install package

```bash
npm install
```

3. `.env` ファイル設定
`.env`ファイルを `.env.example`ファイルから作成してください。
`REACT_APP_API_ENDPOINT`値は、ローカルで実行されているDjangoサーバー。

```bash
REACT_APP_API_ENDPOINT=http://127.0.0.1:8000
```

4. ReactJSのローカル開発サーバーを起動する

```bash
npm start
```
