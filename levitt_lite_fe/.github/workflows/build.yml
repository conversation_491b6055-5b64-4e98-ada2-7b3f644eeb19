name: CI - CD action for Micado FE project

on:
  push:
    branches:
      - dev
  pull_request:
    branches:
      - dev

jobs:
  fe-build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0  # Shallow clones should be disabled for a better relevancy of analysis
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      - run: |
          npm install
          npm run build
      # - uses: sonarsource/sonarqube-scan-action@master
      #   env:
      #     SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      #     SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
      # # If you wish to fail your job when the Quality Gate is red, uncomment the
      # # following lines. This would typically be used to fail a deployment.
      # - uses: sonarsource/sonarqube-quality-gate-action@master
      #   timeout-minutes: 5
      #   env:
      #     SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  # Auto deploy to Staging server
  deploy-staging:
    name: Deploy to Staging server
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/dev'
    env:
      SOURCE_PATH: "/var/www/micado-web-fe"
      GIT_BRANCH: "dev"
      SSH_KEY: ${{ secrets.STG_PRIVATE_KEY }}
      SSH_HOST: ${{ secrets.STG_HOST }}

    steps:
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh/
          echo "$SSH_KEY" > ~/.ssh/staging.key
          chmod 600 ~/.ssh/staging.key
          cat >>~/.ssh/config <<END
          Host staging
            HostName $SSH_HOST
            User ubuntu
            IdentityFile ~/.ssh/staging.key
            StrictHostKeyChecking no
          END
      # Checkout code and build
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      - name: Copy .env file from remote server
        run: |
          rsync -av staging:$SOURCE_PATH/.env ./
      - name: Create build to temp folder
        run: |
          npm install
          npm run build
      - name: Copy build folder to remote server
        run: |
          rsync -avz ./build staging:$SOURCE_PATH/
      # SSH to server and run deploy.sh script
      - name: SSH to server and run deploy.sh script
        run: ssh staging "cd" $SOURCE_PATH "&& sh deploy.sh $GIT_BRANCH"
