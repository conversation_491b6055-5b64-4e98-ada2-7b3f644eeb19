# Cursor Project Rules

## Auto-attach Configuration
# Automatically attach the micado-web directory for backend development
# This ensures the backend rules are applied when working in the Django project

## Rules Application
# Apply development rules from .cursor/rules/
# Backend: micado-web/ -> backend.mdc
# Frontend: micado-web-fe/ -> frontend.mdc
# Configuration: .cursor/settings.json

## Directory Context
# When working in micado-web/ directory, automatically apply:
# - Django development guidelines
# - MySQL database best practices
# - API development standards
# - Security and authentication rules
# - Testing and deployment guidelines

# When working in micado-web-fe/ directory, automatically apply:
# - ReactJS development guidelines
# - JavaScript ES6+ best practices
# - Redux Toolkit state management
# - Material-UI component standards
# - Frontend testing and optimization

## File Structure
```
.cursor/
├── settings.json           # Auto-attach configuration
└── rules/
    ├── backend.mdc        # Backend development rules
    └── frontend.mdc       # Frontend development rules
```

## Usage
# This configuration enables automatic context loading for both backend and frontend
# - Backend rules are automatically applied when working in the micado-web/ directory
# - Frontend rules are automatically applied when working in the micado-web-fe/ directory
# Rules ensure consistent development practices across the entire project
