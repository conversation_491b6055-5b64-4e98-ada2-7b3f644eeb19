#!/bin/bash

# Quick Local Development Startup Script
# Usage: ./start_local_dev.sh [start|stop|status|restart]

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
BACKEND_DIR="./levitt_lite_be"
FRONTEND_DIR="./levitt_lite_fe"
ACTION=${1:-"start"}

# PID files
DJANGO_PID="$BACKEND_DIR/django.pid"
CELERY_PID="$BACKEND_DIR/celery.pid"
FRONTEND_PID="$FRONTEND_DIR/frontend.pid"

print_status() {
    local service=$1
    local status=$2
    if [ "$status" = "OK" ]; then
        echo -e "  ${GREEN}✓${NC} $service"
    elif [ "$status" = "FAIL" ]; then
        echo -e "  ${RED}✗${NC} $service"
    else
        echo -e "  ${YELLOW}⚠${NC} $service: $status"
    fi
}

# Function to start services
start_services() {
    echo -e "${BLUE}=== Starting Local Development Environment ===${NC}"
    echo ""
    
    # Start MySQL if not running
    echo -e "${BLUE}1. Starting MySQL...${NC}"
    if ! mysql -u root -e "SELECT 1" >/dev/null 2>&1; then
        brew services start mysql
        sleep 3
    fi
    print_status "MySQL" "OK"
    
    # Start Redis if not running
    echo -e "${BLUE}2. Starting Redis...${NC}"
    if ! redis-cli ping | grep -q PONG 2>/dev/null; then
        brew services start redis
        sleep 2
    fi
    print_status "Redis" "OK"
    
    # Start Django Backend
    echo -e "${BLUE}3. Starting Django Backend...${NC}"
    if [ -f "$DJANGO_PID" ] && kill -0 $(cat "$DJANGO_PID") 2>/dev/null; then
        print_status "Django" "Already running"
    else
        cd "$BACKEND_DIR"
        source venv/bin/activate
        
        # Run migrations if needed
        python manage.py migrate --run-syncdb >/dev/null 2>&1
        
        # Start Django server
        nohup python manage.py runserver 127.0.0.1:8000 > django.log 2>&1 &
        echo $! > django.pid
        cd ..
        sleep 3
        
        if curl -s http://127.0.0.1:8000/api/ >/dev/null 2>&1; then
            print_status "Django" "OK (http://127.0.0.1:8000)"
        else
            print_status "Django" "FAIL"
        fi
    fi
    
    # Start Celery Worker
    echo -e "${BLUE}4. Starting Celery Worker...${NC}"
    if [ -f "$CELERY_PID" ] && kill -0 $(cat "$CELERY_PID") 2>/dev/null; then
        print_status "Celery" "Already running"
    else
        cd "$BACKEND_DIR"
        source venv/bin/activate
        nohup celery -A main worker --loglevel=info > celery.log 2>&1 &
        echo $! > celery.pid
        cd ..
        sleep 2
        print_status "Celery" "OK"
    fi
    
    # Start Frontend
    echo -e "${BLUE}5. Starting React Frontend...${NC}"
    if [ -f "$FRONTEND_PID" ] && kill -0 $(cat "$FRONTEND_PID") 2>/dev/null; then
        print_status "Frontend" "Already running"
    else
        cd "$FRONTEND_DIR"
        
        # Install dependencies if node_modules doesn't exist
        if [ ! -d "node_modules" ]; then
            echo -e "  ${YELLOW}Installing npm dependencies...${NC}"
            npm install
        fi
        
        # Start React dev server
        nohup npm start > frontend.log 2>&1 &
        echo $! > frontend.pid
        cd ..
        sleep 5
        
        if curl -s http://localhost:3000 >/dev/null 2>&1; then
            print_status "Frontend" "OK (http://localhost:3000)"
        else
            print_status "Frontend" "Starting..."
        fi
    fi
    
    echo ""
    echo -e "${GREEN}=== Development Environment Started ===${NC}"
    echo -e "${BLUE}URLs:${NC}"
    echo "  - Frontend: http://localhost:3000"
    echo "  - Backend API: http://127.0.0.1:8000/api/"
    echo "  - Django Admin: http://127.0.0.1:8000/admin/"
    echo ""
    echo -e "${BLUE}Logs:${NC}"
    echo "  - Django: tail -f $BACKEND_DIR/django.log"
    echo "  - Celery: tail -f $BACKEND_DIR/celery.log"
    echo "  - Frontend: tail -f $FRONTEND_DIR/frontend.log"
}

# Function to stop services
stop_services() {
    echo -e "${BLUE}=== Stopping Local Development Environment ===${NC}"
    echo ""
    
    # Stop Django
    if [ -f "$DJANGO_PID" ]; then
        if kill $(cat "$DJANGO_PID") 2>/dev/null; then
            print_status "Django" "Stopped"
        else
            print_status "Django" "Not running"
        fi
        rm -f "$DJANGO_PID"
    fi
    
    # Stop Celery
    if [ -f "$CELERY_PID" ]; then
        if kill $(cat "$CELERY_PID") 2>/dev/null; then
            print_status "Celery" "Stopped"
        else
            print_status "Celery" "Not running"
        fi
        rm -f "$CELERY_PID"
    fi
    
    # Stop Frontend
    if [ -f "$FRONTEND_PID" ]; then
        if kill $(cat "$FRONTEND_PID") 2>/dev/null; then
            print_status "Frontend" "Stopped"
        else
            print_status "Frontend" "Not running"
        fi
        rm -f "$FRONTEND_PID"
    fi
    
    # Kill any remaining processes
    pkill -f "manage.py runserver" 2>/dev/null || true
    pkill -f "celery.*worker" 2>/dev/null || true
    pkill -f "react-scripts start" 2>/dev/null || true
    
    echo -e "${GREEN}All services stopped.${NC}"
}

# Function to check status
check_status() {
    echo -e "${BLUE}=== Local Development Status ===${NC}"
    echo ""
    
    # Check MySQL
    if mysql -u root -e "SELECT 1" >/dev/null 2>&1; then
        print_status "MySQL" "OK"
    else
        print_status "MySQL" "FAIL"
    fi
    
    # Check Redis
    if redis-cli ping | grep -q PONG 2>/dev/null; then
        print_status "Redis" "OK"
    else
        print_status "Redis" "FAIL"
    fi
    
    # Check Django
    if [ -f "$DJANGO_PID" ] && kill -0 $(cat "$DJANGO_PID") 2>/dev/null; then
        if curl -s http://127.0.0.1:8000/api/ >/dev/null 2>&1; then
            print_status "Django" "OK (PID: $(cat $DJANGO_PID))"
        else
            print_status "Django" "Running but not responding"
        fi
    else
        print_status "Django" "Not running"
    fi
    
    # Check Celery
    if [ -f "$CELERY_PID" ] && kill -0 $(cat "$CELERY_PID") 2>/dev/null; then
        print_status "Celery" "OK (PID: $(cat $CELERY_PID))"
    else
        print_status "Celery" "Not running"
    fi
    
    # Check Frontend
    if [ -f "$FRONTEND_PID" ] && kill -0 $(cat "$FRONTEND_PID") 2>/dev/null; then
        if curl -s http://localhost:3000 >/dev/null 2>&1; then
            print_status "Frontend" "OK (PID: $(cat $FRONTEND_PID))"
        else
            print_status "Frontend" "Starting..."
        fi
    else
        print_status "Frontend" "Not running"
    fi
}

# Main execution
case "$ACTION" in
    "start")
        start_services
        ;;
    "stop")
        stop_services
        ;;
    "status")
        check_status
        ;;
    "restart")
        stop_services
        sleep 2
        start_services
        ;;
    *)
        echo -e "${RED}Invalid action. Use: start, stop, status, or restart${NC}"
        echo "Usage: $0 [start|stop|status|restart]"
        echo ""
        echo "Actions:"
        echo "  start   - Start all development services"
        echo "  stop    - Stop all development services"
        echo "  status  - Check status of all services"
        echo "  restart - Restart all services"
        exit 1
        ;;
esac
