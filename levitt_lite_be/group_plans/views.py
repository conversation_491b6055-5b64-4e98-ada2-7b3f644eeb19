from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import viewsets, status
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from plans.models import Plan
from users.views import IsAdminUser, IsTokenValid
from .models import GroupPlans
from .serializers import GroupPlanSerializer, GroupPlanDeleteSerializer, GroupPlansUpdateSerializer
from rest_framework.views import APIView
from plans.serializers import PlanSerializer
from django.core.paginator import Paginator


class CustomPaginationClassGroupPlans(PageNumberPagination):
    page_size = 15
    page_size_query_param = 'page_size'


class GroupPlanViewSet(viewsets.ModelViewSet):
    queryset = GroupPlans.objects.all().order_by('-id')
    serializer_class = GroupPlanSerializer

    def search_param(self):
        search_param = self.request.query_params.get('search_param', '')

        queryset = self.queryset.all()
        if search_param:
            queryset = queryset.filter(name__icontains=search_param)

        queryset = queryset.filter(user=self.request.user)
        return queryset

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    @swagger_auto_schema(manual_parameters=[
        openapi.Parameter('search_param', openapi.IN_QUERY, description="", type=openapi.TYPE_STRING)])
    def list(self, request, *args, **kwargs):
        queryset = self.search_param()
        start_time = request.query_params.get('start_time')
        end_time = request.query_params.get('end_time')

        paginator = CustomPaginationClassGroupPlans()
        page = paginator.paginate_queryset(queryset, request)

        if page is not None:
            serializer = self.get_serializer(page, many=True, context={'start_time': start_time, 'end_time': end_time})
            for group_plan in serializer.data:
                number_reservations = group_plan.get('number_reservations', 0)
                reservation_amount = group_plan.get('reservation_amount', 0)
                group_plan['avarege_price'] = reservation_amount / number_reservations if number_reservations else 0

            custom_data = {
                "total_pages": paginator.page.paginator.num_pages,
                "message": "Group plans fetched successfully",
                "data": serializer.data,
                "status": status.HTTP_200_OK
            }
            return paginator.get_paginated_response(custom_data)

        serializer = self.get_serializer(queryset, many=True, context={'start_time': start_time, 'end_time': end_time})
        for group_plan in serializer.data:
            number_reservations = group_plan.get('number_reservations', 0)
            reservation_amount = group_plan.get('reservation_amount', 0)
            group_plan['avarege_price'] = reservation_amount / number_reservations if number_reservations else 0

        custom_data = {
            "total_pages": paginator.page.paginator.num_pages,
            "message": "Group plans fetched successfully",
            "data": serializer.data,
            "status": status.HTTP_200_OK
        }
        return Response(custom_data)

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)

        related_plans = Plan.objects.filter(group_plan=instance).order_by('-id')
        paginator = Paginator(related_plans, per_page=15)
        page_number = request.GET.get('page', 1)
        page = paginator.get_page(page_number)

        related_plans_data = [
            {
                "id": plan.id,
                "avarege_price": PlanSerializer().get_avarege_price(plan),
                "benefits": plan.benefits,
                "checkin_time": plan.checkin_time,
                "checkout_time": plan.checkout_time,
                "create_at": plan.create_at,
                "name": plan.name,
                "number_reservations": PlanSerializer().get_number_reservations(plan),
                "number_room": plan.number_room,
                "plan_description": plan.plan_description,
                "reservation_amount": PlanSerializer().get_reservation_amount(plan),
                "reservation_site": plan.reservation_site,
                "reservation_time": plan.reservation_time,
                "room_type": plan.room_type,
                "total_fee": plan.total_fee,
            }
            for plan in related_plans
        ]

        custom_data = {
            "group_plan_detail": serializer.data,

            "related_plans": {
                "related_plans": related_plans_data,
                "total_pages": paginator.num_pages,
                "current_page": page_number,
                "next_page": page.next_page_number() if page.has_next() else None,
                "previous_page": page.previous_page_number() if page.has_previous() else None,
            }
        }

        return Response(custom_data)


class GroupPlanDeleteMultiView(APIView):
    @swagger_auto_schema(
        request_body=GroupPlanDeleteSerializer,
        request_body_examples={
            'example-1': {
                'value': {"ids": [1, 2, 3]},
                'summary': 'Example Request Body',
                'description': 'Provide a list of plan group IDs to delete.',
            }
        }
    )
    def post(self, request):
        serializer = GroupPlanDeleteSerializer(data=request.data)

        if serializer.is_valid():
            ids = serializer.validated_data.get('ids')
        else:
            return Response({
                'message': '削除用のIDが提供されていません。',
                'errors': serializer.errors,
                'status': status.HTTP_400_BAD_REQUEST,
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            group_plans = GroupPlans.objects.filter(id__in=ids)
            for group_plan in group_plans:
                group_plan.plans.all().update(group_plan=None)
            group_plans.delete()

            return Response({
                'message': '選択したプラングループが正常に削除されました。',
                'status': status.HTTP_200_OK,
            }, status=status.HTTP_200_OK)

        except GroupPlans.DoesNotExist:
            return Response({
                'message': 'プラングループが見つかりません。',
                'status': status.HTTP_404_NOT_FOUND,
            }, status=status.HTTP_404_NOT_FOUND)


class GroupPlansUpdateView(APIView):
    @swagger_auto_schema(
        request_body=GroupPlansUpdateSerializer,
        request_body_examples={
            'example-1': {
                'value': {"ids": [1, 2, 3]},
                'summary': 'Example Request Body',
                'description': 'Provide a list of plan IDs.',
            }
        }
    )
    def put(self, request, group_id):
        # Validate the input data
        serializer = GroupPlansUpdateSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        # Retrieve the group instance
        try:
            group = GroupPlans.objects.get(pk=group_id)
        except GroupPlans.DoesNotExist:
            return Response({
                "message": "Group not found",
                "status": status.HTTP_404_NOT_FOUND
            }, status=status.HTTP_404_NOT_FOUND)

        # Retrieve the selected plan IDs and update the plans field
        plan_ids = serializer.validated_data["plan_ids"]
        plans = Plan.objects.filter(pk__in=plan_ids)
        list_plan_in_group = Plan.objects.filter(group_plan_id=group_id)
        combined_plans = list(plans) + list(list_plan_in_group)
        # Set the plans associated with the group
        group.plans.set(combined_plans)

        return Response({
            "message": "Plans updated successfully",
            "status": status.HTTP_200_OK
        }, status=status.HTTP_200_OK)
