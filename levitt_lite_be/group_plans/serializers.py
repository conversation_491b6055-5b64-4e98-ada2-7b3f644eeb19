from rest_framework import serializers
from .models import GroupPlans
from plans.serializers import PlanSerializer
from datetime import datetime


class GroupPlanSerializer(serializers.ModelSerializer):
    name = serializers.CharField(required=False, allow_null=True, label="プラングループ名")
    number_reservations = serializers.IntegerField(required=False)
    reservation_amount = serializers.IntegerField(required=False)
    average_price = serializers.IntegerField(required=False)

    class Meta:
        model = GroupPlans
        fields = [
            'id', 'name', 'number_reservations', 'reservation_amount', 'average_price'
        ]

    def get_filtered_plans(self, instance):
        start_time = self.context.get('start_time')
        end_time = self.context.get('end_time')

        # Filter out plans with null reservation_time
        plans = instance.plans.filter(reservation_time__isnull=False)

        if start_time and end_time:
            start_time = datetime.fromisoformat(start_time)
            end_time = datetime.fromisoformat(end_time)
            return plans.filter(reservation_time__gte=start_time, reservation_time__lte=end_time)

        return plans

    def get_number_reservations(self, instance):
        total_number_reservations = 0
        for plan in self.get_filtered_plans(instance):
            plan_serializer = PlanSerializer(plan)
            total_number_reservations += plan_serializer.get_number_reservations(plan)
        return total_number_reservations

    def get_reservation_amount(self, instance):
        total_reservation_amount = 0
        for plan in self.get_filtered_plans(instance):
            plan_serializer = PlanSerializer(plan)
            total_reservation_amount += plan_serializer.get_reservation_amount(plan)

        return total_reservation_amount

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['number_reservations'] = self.get_number_reservations(instance)
        representation['reservation_amount'] = self.get_reservation_amount(instance)
        representation['total_plans'] = instance.plans.count()
        return representation


class GroupPlanDeleteSerializer(serializers.Serializer):
    ids = serializers.ListField(child=serializers.IntegerField())


class GroupPlansUpdateSerializer(serializers.Serializer):
    plan_ids = serializers.ListField(child=serializers.IntegerField())
