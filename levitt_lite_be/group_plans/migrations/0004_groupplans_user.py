# Generated by Django 4.2.6 on 2023-11-04 15:21

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("group_plans", "0003_remove_groupplans_average_price_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="groupplans",
            name="user",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="group_plans",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
