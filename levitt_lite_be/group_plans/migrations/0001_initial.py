# Generated by Django 4.2.5 on 2023-10-22 13:44

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='GroupPlans',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('group_name', models.Char<PERSON>ield(max_length=100, null=True, verbose_name='プラングループ名')),
                ('sales_start_time', models.Char<PERSON>ield(max_length=255, null=True)),
                ('sales_end_time', models.Char<PERSON>ield(max_length=255, null=True)),
                ('reservation_count', models.IntegerField(null=True, verbose_name='予約件数')),
                ('booking_amount', models.DecimalField(decimal_places=2, max_digits=10, null=True, verbose_name='予約金額')),
                ('average_price', models.DecimalField(decimal_places=2, max_digits=10, null=True, verbose_name='平均単価')),
            ],
            options={
                'db_table': 'group_plans',
            },
        ),
    ]
