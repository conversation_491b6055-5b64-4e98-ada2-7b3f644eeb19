[uwsgi]
# command start: uwsgi --ini uwsgi.ini --daemonize uwsgi.log
# command stop: uwsgi --stop running_app.pid
module = main.wsgi:application
master = true
processes = 3
http-socket = :8000
chmod-socket = 660
vacuum = true
die-on-term = true

# Logging configuration
logto = uwsgi.log
log-reopen = true
log-backupname = uwsgi.log.old
log-maxsize = 1000000
log-maxfiles = 5

# Path to virtual environment
virtualenv = myenv

# Path to PID file
pidfile = running_app.pid
