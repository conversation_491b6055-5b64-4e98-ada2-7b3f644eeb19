from django.contrib import admin
from django.urls import path
from rest_framework.routers import <PERSON><PERSON>ult<PERSON><PERSON><PERSON>
from rest_framework_simplejwt.views import TokenRefreshView
from group_plans.views import GroupPlanViewSet, GroupPlanDeleteMultiView, GroupPlansUpdateView
from hotels.views import HotelViewSet, ApplyRegistrationHotelViewSet, CompetitionRegistrationViewSet
from notifications.views import NotificationViewSet, NotificationSelectOptionsView
from otas.views import OtaViewSet, OtaSelectOptionsView
from plans.views import PlanViewSet, PlanUpdateGroupPlanIdView, PlanCSVImportView, PlanListAPIView, \
    PlanPartialUpdateView, CustomHeadersImportView
from users.views import UserLoginView, UserProfileView, LogoutView, UpdateUserView, \
    DeleteUserView, ListUsersView, UserDetailView, ActivateUserView, RegisterUserView, CreateUserView, \
    PasswordResetRequestView, PasswordResetView, UsersOptionView, UserIsActiveView, UpdateProfileView, \
    DeleteProfileView, UpdatePartnerIdView, UpdateNotificationEmailSettingView
from gpt_plans.views import ResistGPTPlanView, ListAllGPTPlansView, ListGPTPlansView, \
    FetchGPTPlansView, GetGPTPlanAsyncView, CreateGPTPlanAsyncView
from reports.views import ReportOverviewView, ReportPlanOtaRanking, ReportPlanByDayOfWeek, ReportTopRoomType, \
    ReportLeadTimePrice, ReportGroupPlanEachRoomTypeRanking, ReportDaysOfWeekOverview
from contact.views import ContactView
from django.urls import include
from drf_yasg.views import get_schema_view
from drf_yasg import openapi
from django.conf import settings
from django.conf.urls.static import static
# from gpt_plans.views import CreateGPTPlanView, ResistGPTPlanView, ListAllGPTPlansView, ListGPTPlansView, FetchGPTPlansView
#     DeleteProfileView


import os
from dotenv import load_dotenv
load_dotenv()

# Hotels router
router = DefaultRouter()
router.register(r'notifications', NotificationViewSet)
router.register(r'otas', OtaViewSet)
router.register(r'plans', PlanViewSet)
router.register(r'group_plans', GroupPlanViewSet)
router.register(r'apply_restration_hotel', ApplyRegistrationHotelViewSet)
router.register(r'hotels', HotelViewSet, basename='hotel')
router.register(r'competition-registration', CompetitionRegistrationViewSet, basename='competition-registration')

schema_view = get_schema_view(openapi.Info(
    title="Levitt API schema",
    default_version='v1'),
    public=True,
    permission_classes=(),
    url=os.getenv('SWAGGER_CLIENT_URL'),
)

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/group_plans/delete-multiple/', GroupPlanDeleteMultiView.as_view(), name='group-plan-delete-multiple'),
    path('api/plans/update-multiple/', PlanPartialUpdateView.as_view(), name='update-multiple-plans'),
    path('api/', include(router.urls)),
    path('api/notifications/select-options/', NotificationSelectOptionsView.as_view(), name='notification-select-options'),
    path('api/users/select-options/', UsersOptionView.as_view(), name='notification-select-options'),
    path('api/ota/select-options/', OtaSelectOptionsView.as_view(), name='ota-select-options'),
    path('api/plans/<int:pk>/update/', PlanUpdateGroupPlanIdView.as_view(), name='plan-update'),
    path('api/plans/import', PlanCSVImportView.as_view(), name='import-plan-csv'),
    path('api/plans/header-import', CustomHeadersImportView.as_view(), name='header-import-plan-csv'),
    path('api/plans/create-multiple', PlanListAPIView.as_view(), name='plan-create-multiple'),
    # path('api/gpt_plans/create/', CreateGPTPlanView.as_view(), name='create_plan'),
    path('api/gpt_plans/create/asy/', CreateGPTPlanAsyncView.as_view(), name='async-create-plan'),
    path('api/gpt_plans/get/<str:task_id>', GetGPTPlanAsyncView.as_view(), name='async-get-plan'),
    path('api/gpt_plans/resister/', ResistGPTPlanView.as_view(), name='resist_gpt_plan'),
    path('api/gpt_plans/list-gpt-plans-all/', ListAllGPTPlansView.as_view(), name='list-gpt-plans-all'),
    path('api/gpt_plans/list-gpt-plans/', ListGPTPlansView.as_view(), name='list-gpt-plans'),
    path('api/gpt_plans/fetch-gpt-plan/', FetchGPTPlansView.as_view(), name='fetch-gpt-plan'),
    path('api/group_plans/<int:group_id>/update/', GroupPlansUpdateView.as_view(), name='group-plans-update'),
    path('api/user_is_active/<str:email>/', UserIsActiveView.as_view(), name='user-is-active'),
    path('api/users/register', RegisterUserView.as_view(), name='register_user'),
    path('api/contact/', ContactView.as_view(), name='contact'),
    path('api/token/', UserLoginView.as_view(), name='token_obtain_pair'),
    path('api/me/', UserProfileView.as_view(), name='user-profile'),
    path('api/logout/', LogoutView.as_view(), name='logout'),
    path('api/update-profile', UpdateProfileView.as_view(), name='update-profile'),
    path('api/users/activate', ActivateUserView.as_view(), name='activate_user'),
    path('api/users/create', CreateUserView.as_view(), name="create_user"),
    path('api/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('api/update-user/<int:pk>/', UpdateUserView.as_view(), name='update-user'),
    path('api/delete-user/<int:pk>/', DeleteUserView.as_view(), name='delete-user'),
    path('api/delete-profile', DeleteProfileView.as_view(), name='delete-profile'),
    path('api/user/<int:pk>/', UserDetailView.as_view(), name='user-detail'),
    path('api/list-users/', ListUsersView.as_view(), name='list-users'),
    path('api/password-reset/', PasswordResetRequestView.as_view(), name='password-reset-request'),
    path('api/password-reset/<str:uid>/<str:token>/', PasswordResetView.as_view(), name='password-reset'),
    path('api/users/update_partner_id/', UpdatePartnerIdView.as_view(), name='update_partner_id'),
    path('api/users/update-notification-settings/', UpdateNotificationEmailSettingView.as_view(), name='update-notification-settings'),
    path('api/report/overview', ReportOverviewView.as_view(), name='report-overview'),
    path('api/report/plan-ota-ranking', ReportPlanOtaRanking.as_view(), name='report-plan-ota-ranking'),
    path('api/report/plan-by-days-of-week', ReportPlanByDayOfWeek.as_view(), name='report-plan-by-days-of-week'),
    path('api/report/plan-top-room-type', ReportTopRoomType.as_view(), name='report-top-room-type'),
    path('api/report/plan-lead-time-price', ReportLeadTimePrice.as_view(), name='report-lead-time-price'),
    path('api/report/group-plan-each-room-type', ReportGroupPlanEachRoomTypeRanking.as_view(), name='report-group-plan-each-room-type'),
    path('api/report/days-of-week-overview', ReportDaysOfWeekOverview.as_view(), name='report-days-of-week-overview'),
    path('docs/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
] + static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
