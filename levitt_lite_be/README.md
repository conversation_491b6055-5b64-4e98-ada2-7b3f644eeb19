# Micado Django Server

[![Python](https://img.shields.io/badge/Python-3776AB?style=for-the-badge&logo=python&logoColor=white)](https://www.python.org/)
[![Django](https://img.shields.io/badge/Django-092E20?style=for-the-badge&logo=django&logoColor=white)](https://www.djangoproject.com/)
[![OpenAI](https://img.shields.io/static/v1?style=for-the-badge&message=OpenAI&color=412991&logo=OpenAI&logoColor=FFFFFF&label=)](https://openai.com/research/overview)
[![Pinecone](https://img.shields.io/badge/Pinecone-032754?style=for-the-badge)](https://www.pinecone.io/)
[![Mailtrap](https://img.shields.io/badge/Mailtrap-37d172?style=for-the-badge)](https://mailtrap.io/home)

Micadoプロジェクトのサーバーバックエンド。Djangoフレームワークを使用してプランを作成するAPI。

## システム要件

macOSまたはLinux

- Python: [Pythonのインストールガイド](https://www.python.org/downloads/macos/)
- PiP: [PiPのインストールガイド](https://pip.pypa.io/en/stable/installation/)

## 開発環境

使用する技術

- **python:** 3.9.6
- **pip:** 23.2

## Pinecone Databseサービスサービスに登録する

1. Pinecone Databseサービス登録
[Pinecone登録URL](https://app.pinecone.io/?sessionType=login)

2. ログインできあれば、**organizations**　ページに **「Create a project」** ボタンを行う。

<div style="padding-left: 30px;">
参照画像：
<img src="https://i.ibb.co/sJPzWgT/screencapture-app-pinecone-io-organizations-Nbn-Q5-VC-SZ7-PKu4-AOy-N-projects-create-2023-09-06-14-1.png" alt="" width="50%">
</div>

3. Pineconeプロジェクトの作成が完了するまで数分お待ちください。

4. 完了したら、PineconeのベクトルDBを成功裏に初期化した。

## Mailtrapサービスに登録する

[Mailtrapウェブサイト](https://mailtrap.io/)をアクセスし、Mailtrapサービスに登録する。

## インストール手順

1. プロジェクトのクローン

```bash
<NAME_EMAIL>:HapoDivOne/micado-web.git
cd micado-web
```

2. 下コマンドを実行して **Python venv** を使用して **New Virtual Environment** を作成してください。

```bash
python3.11 -m venv env
```

3. 下コマンドを実行して `Virtual Environment` をアクティブにしてください。次回以降、 `Virtual Environment` を再作成せずにactiveコマンドを実行するだけを行う。

```bash
source env/bin/activate
```

4. Install python libs:

```bash
pip install --upgrade pip
pip install -r requirements.txt
```

5. `migration` を実装する

```bash
python manage.py makemigrations
python manage.py migrate
```

上記の手順で、Micado Django プロジェクトのインストールが完了しました。次のステップは、プロジェクトの環境を `.env` ファイルで設定することです。

## .envファイル設定

1. クロールしたDBをMysqlにインポートしてください。

2. Mysqlを設定する。下の `.env`変数を更新ください。

```bash
DB_HOST='localhost'
DB_PORT='3306'
DB_USER='mysql_user'
DB_PASSWORD='mysql_password'
DB_NAME='database_name'
```

3. 次、Pineconeを設定する。
Pineconeの値を取得するには、画像のようにアクセスしてください。

<div style="padding-left: 30px;"><img src="https://i.ibb.co/SB79Qtw/Screenshot-2023-09-06-at-14-58-51.png" alt="" width="50%"></div>

画像のように２つ値をコピーしてください。PINECONE_INDEX_NAMEの値は自由に設定する。
<div style="padding-left: 30px;"><img src="https://i.ibb.co/yQ3HHkq/Screenshot-2023-09-06-at-15-02-34.png" alt="" width="50%"></div>

下に更新してください。

```bash
PINECONE_API_KEY="***"
PINECONE_ENVIRONMENT="gcp-starter"
PINECONE_INDEX_NAME="travel-plans"
```

4. Open AIキーも更新してください。

```bash
EMBED_MODEL="text-embedding-ada-002"
OPENAI_API_KEY="sk-*********"
```

5. Mailtrapキーの更新
メール送信サービスを使用するには、Mailtrap envを変更してください。
Mailtrapにアクセスして、`Email Testing` → `Inbox` → `My Inbox` → `SMTP Settings` に移動し、Djangoを選択して画像のように値をコピーし、envに貼り付けてください。
<div style="padding-left: 30px;"><img src="https://i.ibb.co/J2cHD3m/Screenshot-2023-09-08-at-10-53-47.png" alt="" width="50%"></div>

```bash
# Mail settings
EMAIL_HOST = 'sandbox.smtp.mailtrap.io'
EMAIL_HOST_USER = 'input_mailtrap_user'
EMAIL_HOST_PASSWORD = 'input_mailtrap_password'
EMAIL_PORT = '2525'
```

上記の手順で、`.env` ファイルで設定できた。

## Init vector DB and Upsert

1. ベクトルDBを初期化: PineconeプロジェクトのIndexを作成し、DBにサンプルデータを追加する。

```bash
python plans/db_create.py
```

2. Upsert DB: ベクトルを更新し、ホテルデータをベクトルDBにInsertする。

```bash
python plans/index_plans.py
```

## Run local server & call API

1. ローカル開発サーバーを起動する

```bash
python manage.py runserver
```

2. テスト

```bash
curl --location --request POST 'http://127.0.0.1:8000/api/plans/create' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer bearer-token' \
--data-raw '{
    "meal_type": "夕",
    "date_select_type": 2,
    "days_type": "平日",
    "input_text": "create a travel plan with 10,000yen/people",
    "room_type": "シングルルーム",
    "sale_end_date": "23/08/2023",
    "sale_start_date": "22/08/2023",
    "perks": "アーリーチェックイン"
}'
```
