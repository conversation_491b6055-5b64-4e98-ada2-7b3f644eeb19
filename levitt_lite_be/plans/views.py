import csv
from datetime import datetime
from django.utils import timezone
from django.db.models import Q
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import viewsets, status, generics
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from users.views import IsAdminUser, IsTokenValid
from .models import Plan
from group_plans.models import GroupPlans
from .serializers import PlanSerializer, PlanUpdateGroupPlanIdSerializer, PlanCSVImportSerializer, GetPlansSerializer, \
    PlanPartialUpdateSerializer, CustomHeadersImportSerializer
from django.core.exceptions import ValidationError
from .utils import parse_date
from .constants import HEADER_CONFIG
from datetime import timedelta

class CustomPaginationClassPlans(PageNumberPagination):
    page_size = 15
    page_size_query_param = 'page_size'

    def get_paginated_response(self, data):
        return Response({
            'next': self.get_next_link(),
            'previous': self.get_previous_link(),
            'count': self.page.paginator.count,
            'total_pages': self.page.paginator.num_pages,
            'results': data
        })


class PlanViewSet(viewsets.ModelViewSet):
    queryset = Plan.objects.all().order_by('-id')
    serializer_class = PlanSerializer

    def get_permissions(self):
        permission_classes = [IsTokenValid, IsAuthenticated]
        return [permission() for permission in permission_classes]

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    def search_param(self):
        search_param = self.request.query_params.get('search_param', '')
        group_plan = self.request.query_params.get('group_plan_id', '')
        queryset = self.queryset.filter(Q(name__icontains=search_param))

        start_time_str = self.request.query_params.get('start_time')
        end_time_str = self.request.query_params.get('end_time')

        if group_plan:
            queryset = queryset.filter(group_plan=group_plan)

        # Filter out plans with null reservation_time
        queryset = queryset.filter(reservation_time__isnull=False)

        if start_time_str:
            try:
                start_time = datetime.fromisoformat(start_time_str)
                queryset = queryset.filter(reservation_time__gte=start_time)
            except ValueError:
                return Response(
                    {'error': 'Invalid start time format'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        if end_time_str:
            try:
                end_time = datetime.fromisoformat(end_time_str)
                queryset = queryset.filter(reservation_time__lte=end_time)
            except ValueError:
                return Response(
                    {'error': 'Invalid end time format'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # If both start and end times are not provided, filter for the entire month
        if not start_time_str and not end_time_str:
            current_date = timezone.now()
            first_day_of_month = current_date.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            last_day_of_month = first_day_of_month.replace(month=first_day_of_month.month + 1)

            queryset = queryset.filter(reservation_time__gte=first_day_of_month, reservation_time__lt=last_day_of_month)

        # Filter the queryset to include plans only for the current user
        queryset = queryset.filter(user=self.request.user).order_by('name')

        return queryset

    @swagger_auto_schema(manual_parameters=[
        openapi.Parameter('search_param', openapi.IN_QUERY, description="Filter by plan name",
                          type=openapi.TYPE_STRING),
        openapi.Parameter('start_time', openapi.IN_QUERY, description="Filter start time",
                          type=openapi.FORMAT_DATE),
        openapi.Parameter('end_time', openapi.IN_QUERY, description="Filter end time",
                          type=openapi.FORMAT_DATE),
        openapi.Parameter('group_plan_id', openapi.IN_QUERY, description="Filter by group_plan ID",
                          type=openapi.TYPE_INTEGER)
    ])
    def _group_duplicate_plans(self, queryset):
        """
        Group duplicate plans by name + reservation_site
        Returns list of representative plans (1 plan per group)
        """
        grouped_plans = {}

        for plan in queryset:
            # Create key to group duplicate plans
            group_key = f"{plan.name}_{plan.reservation_site}"

            if group_key not in grouped_plans:
                # Save first plan as representative for the group
                grouped_plans[group_key] = plan

        # Return list of representative plans
        return list(grouped_plans.values())

    def list(self, request, *args, **kwargs):
        queryset = self.search_param()

        # Plans are already grouped during import, so no need to group again
        unique_plans = list(queryset)

        # Calculate totals from all plans (not just current page)
        all_serializer = self.get_serializer(unique_plans, many=True)
        total_number_reservations = sum(item['number_reservations'] for item in all_serializer.data)
        total_reservation_amount = sum(item['reservation_amount'] for item in all_serializer.data)
        # Calculate average price correctly: total_amount / total_reservations
        total_avarege_price = total_reservation_amount / total_number_reservations if total_number_reservations > 0 else 0

        paginator = CustomPaginationClassPlans()
        page = paginator.paginate_queryset(unique_plans, request)

        if page is not None:
            page_serializer = self.get_serializer(page, many=True)

            custom_data = {
                "message": "Plans fetched successfully",
                "data": page_serializer.data,
                "status": status.HTTP_200_OK,
                "total_number_reservations": total_number_reservations,
                "total_avarege_price": total_avarege_price,
                "total_reservation_amount": total_reservation_amount
            }
            return paginator.get_paginated_response(custom_data)

        custom_data = {
            "message": "Plans fetched successfully",
            "data": all_serializer.data,
            "status": status.HTTP_200_OK,
            "total_number_reservations": total_number_reservations,
            "total_avarege_price": total_avarege_price,
            "total_reservation_amount": total_reservation_amount
        }

        return Response(custom_data)


class PlanUpdateGroupPlanIdView(generics.UpdateAPIView):
    permission_classes = [IsTokenValid, IsAuthenticated, IsAdminUser]

    queryset = Plan.objects.all()
    serializer_class = PlanUpdateGroupPlanIdSerializer

    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        custom_data = {
            "message": "Plan updated successfully",
            "data": serializer.data,
        }

        return Response(custom_data, status=status.HTTP_200_OK)


class PlanCSVImportView(APIView):
    permission_classes = [IsTokenValid, IsAuthenticated]

    @swagger_auto_schema(request_body=PlanCSVImportSerializer, responses={200: 'CSV data imported successfully'})
    def post(self, request):
        serializer = PlanCSVImportSerializer(data=request.data)
        if serializer.is_valid():
            csv_file = serializer.validated_data['csv_file']
            headers = serializer.validated_data['headers']
            custom_headers = {}

            for item in headers:
                for key, values in HEADER_CONFIG.items():
                    if item.get('custom', []) in values:
                        custom_headers[key] = item.get('key', [])

            if not all(key in custom_headers for key in HEADER_CONFIG):
                return Response({
                    'message': 'ヘッダーが正しくありません。',
                    'status': status.HTTP_400_BAD_REQUEST,
                    'headers': headers,
                    'header_error': 'Invalid headers'
                }, status=status.HTTP_400_BAD_REQUEST)

            try:
                try:
                    decoded_file = csv_file.read().decode('SHIFT_JIS').splitlines()
                except UnicodeDecodeError:
                    csv_file.seek(0)
                    decoded_file = csv_file.read().decode('utf-8').splitlines()

                csv_reader = csv.DictReader(decoded_file)
                ids = []
                plan_data = {}  # Dictionary to group plans by name

                # First pass: collect and group data by plan name
                for row in csv_reader:
                    if not row[custom_headers['plan_name']]:
                        continue


                    plan_name = row[custom_headers['plan_name']]
                    number_room = int(row[custom_headers['number_room']]) if row[custom_headers['number_room']].strip() else 0
                    total_fee = int(row[custom_headers['total_fee']]) if row[custom_headers['total_fee']].strip() else 0

                    if plan_name not in plan_data:
                        # First occurrence of this plan name - initialize with first record data
                        plan_data[plan_name] = {
                            'name': plan_name,
                            'room_type': row[custom_headers['room_type']],
                            'number_room': 1,  # Count of reservations, not physical rooms
                            'reservation_site': row[custom_headers['reservation_site']],
                            'reservation_time': parse_date(row[custom_headers['reservation_time']]),
                            'total_fee': total_fee,
                            'checkin_time': parse_date(row[custom_headers['checkin_time']]),
                            'checkout_time': parse_date(row[custom_headers['checkout_time']]),
                            'user': request.user
                        }
                    else:
                        # Subsequent occurrences: increment reservation count and sum total_fee
                        plan_data[plan_name]['number_room'] += 1  # Increment reservation count
                        plan_data[plan_name]['total_fee'] += total_fee

                # Second pass: create Plan objects from aggregated data
                for plan in plan_data.values():
                    new_plan = Plan.objects.create(
                        name=plan['name'],
                        room_type=plan['room_type'],
                        number_room=plan['number_room'],
                        reservation_site=plan['reservation_site'],
                        reservation_time=plan['reservation_time'],
                        total_fee=plan['total_fee'],
                        checkin_time=plan['checkin_time'],
                        checkout_time=plan['checkout_time'],
                        user=self.request.user
                    )
                    ids.append(new_plan.id)

                return Response({
                    'message': 'CSV data imported successfully',
                    'status': status.HTTP_201_CREATED,
                    'ids': ids,
                    'header': custom_headers.keys()
                }, status=status.HTTP_201_CREATED)
            except Exception as e:
                return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CustomHeadersImportView(APIView):
    permission_classes = [IsTokenValid, IsAuthenticated]

    @swagger_auto_schema(request_body=CustomHeadersImportSerializer, responses={200: 'CSV data imported successfully'})
    def post(self, request):
        serializer = CustomHeadersImportSerializer(data=request.data)
        if serializer.is_valid():
            csv_file = serializer.validated_data['csv_file']

            try:
                try:
                    decoded_file = csv_file.read().decode('SHIFT_JIS').splitlines()
                except UnicodeDecodeError:
                    csv_file.seek(0)
                    decoded_file = csv_file.read().decode('utf-8').splitlines()

                csv_reader = csv.DictReader(decoded_file)
                headers = []


                all_header_values = set()
                for values in HEADER_CONFIG.values():
                    all_header_values.update(values)

                for row in csv_reader:
                    headers = list(row.keys())
                    break

                header_errors = [x for x in headers if x not in all_header_values]
                header_successes = [x for x in headers if x not in header_errors]

                header_maps = []
                for header in header_successes:
                    for key, values in HEADER_CONFIG.items():
                        if header in values:
                            header_maps.append(values[0])
                            break

                return Response({
                    'message': 'CSV get data header imported successfully',
                    'status': status.HTTP_200_OK,
                    'header_successes': header_successes,
                    'header_errors': header_errors,
                    'header_maps': header_maps,
                }, status=status.HTTP_200_OK)
            except Exception as e:
                return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class PlanListAPIView(APIView):
    permission_classes = [IsTokenValid, IsAuthenticated]

    @swagger_auto_schema(
        request_body=openapi.Schema(type=openapi.TYPE_OBJECT,
            properties={'ids': openapi.Schema(type=openapi.TYPE_STRING, description='Comma-separated list of Plan IDs (e.g., "1,2,3")')},
            required=['ids']
        )
    )
    def post(self, request, *args, **kwargs):
        ids_str = request.data.get('ids', '')

        id_list = [int(id) for id in ids_str.split(',') if id.isdigit()]

        if not id_list:
            return Response({
                'message': '有効なIDsがありません。',
                'status': status.HTTP_400_BAD_REQUEST,
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            plans = Plan.objects.filter(id__in=id_list)
            plan_serializer = PlanSerializer(plans, many=True)

            return Response({
                'results': plan_serializer.data,
                'message': 'Selected plans retrieved successfully.',
                'status': status.HTTP_200_OK,
            }, status=status.HTTP_200_OK)
        except Plan.DoesNotExist:
            return Response({
                'message': f'グループプランID{id_list}が見つかりません。',
                'status': status.HTTP_400_BAD_REQUEST,
            }, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(manual_parameters=[
        openapi.Parameter('ids', openapi.IN_QUERY, description="Filter by ids", type=openapi.TYPE_ARRAY,
                          items=openapi.Items(type=openapi.TYPE_INTEGER))])
    def delete(self, request):
        ids_str = request.query_params.get('ids', '')
        id_list = [int(id) for id in ids_str.split(',') if id.isdigit()]

        if not id_list:
            return Response({
                'message': '有効なIDsがありません。',
                'status': status.HTTP_400_BAD_REQUEST,
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            plans = Plan.objects.filter(id__in=id_list)
            if not plans.exists():
                return Response({
                    'message': f'グループプランID{id_list}が見つかりません。',
                    'status': status.HTTP_404_NOT_FOUND,
                }, status=status.HTTP_404_NOT_FOUND)
            plans.delete()
            return Response({
                'message': 'プランが正常に削除されました。',
                'status': status.HTTP_204_NO_CONTENT,
            }, status=status.HTTP_204_NO_CONTENT)
        except Exception as e:
            return Response({
                'message': '失敗したプランを削除しました。',
                'status': status.HTTP_400_BAD_REQUEST,
            }, status=status.HTTP_400_BAD_REQUEST)


class PlanPartialUpdateView(APIView):
    permission_classes = [IsTokenValid, IsAuthenticated]
    serializer_class = PlanPartialUpdateSerializer
    queryset = Plan.objects.all()

    def get_object(self, obj_id):
        try:
            return Plan.objects.get(id=obj_id)
        except (Plan.DoesNotExist, ValidationError):
            raise status.HTTP_400_BAD_REQUEST

    def validate_ids(self, ids_list):
        for id in ids_list:
            try:
                Plan.objects.get(id=id)
            except (Plan.DoesNotExist, ValidationError):
                raise status.HTTP_400_BAD_REQUEST

    def post(self, request):
        data_list = request.data
        plan_ids = [int(data['id']) for data in data_list]

        if not plan_ids:
            return Response({
                'message': '有効なIDsがありません。',
                'status': status.HTTP_400_BAD_REQUEST,
            }, status=status.HTTP_400_BAD_REQUEST)

        self.validate_ids(plan_ids)
        instances = []
        for data in data_list:
            plan = self.get_object(data['id'])

            if 'group_plan_id' in data:
                group_plan_id = data['group_plan_id']
                try:
                    group_plan = GroupPlans.objects.get(id=group_plan_id)
                    plan.group_plan = group_plan
                except GroupPlans.DoesNotExist:
                    return Response({
                        'message': f'グループプランID{group_plan_id}が見つかりません。',
                        'status': status.HTTP_400_BAD_REQUEST,
                    }, status=status.HTTP_400_BAD_REQUEST)
            plan.save()
            instances.append(plan)
        serializer = PlanPartialUpdateSerializer(instances, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
