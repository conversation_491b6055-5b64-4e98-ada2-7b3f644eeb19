from django.contrib.auth.models import User
from django.db import models
from group_plans.models import GroupPlans


class Plan(models.Model):
    name = models.TextField(verbose_name="プラングループ名", null=True)
    room_type = models.CharField(max_length=255, null=True, verbose_name="部屋タイプ名称")
    number_room = models.IntegerField(null=True, verbose_name="泊数")
    reservation_site = models.TextField(null=True, verbose_name="予約サイト名称")
    reservation_time = models.DateField(null=True, verbose_name="申込日")
    total_fee = models.IntegerField(null=True, verbose_name="料金合計額")
    checkin_time = models.DateField(null=True, verbose_name="チェックイン日")
    checkout_time = models.DateField(null=True, verbose_name="チェックアウト日")
    plan_description = models.TextField(null=True, verbose_name="プラン説明文")
    benefits = models.TextField(null=True, verbose_name="特典")
    group_plan = models.ForeignKey(GroupPlans, on_delete=models.SET_NULL, null=True, related_name='plans')
    # sale_period_start = models.DateField(null=True, verbose_name="販売期間")
    # sale_period_end = models.DateField(null=True, verbose_name="販売期間")
    # accommodation_period_start = models.DateField(null=True, verbose_name="宿泊期間")
    # accommodation_period_end = models.DateField(null=True, verbose_name="宿泊期間")
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='plans')
    create_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)


    class Meta:
        db_table = 'plans'
