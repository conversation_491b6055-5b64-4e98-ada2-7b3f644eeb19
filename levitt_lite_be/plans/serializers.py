from django.contrib.auth.models import User
from rest_framework import serializers
from .models import Plan
from group_plans.models import GroupPlans


class GroupPlansSerializer(serializers.ModelSerializer):
    class Meta:
        model = GroupPlans
        fields = '__all__'


class PlanSerializer(serializers.ModelSerializer):
    number_reservations = serializers.SerializerMethodField()
    reservation_amount = serializers.SerializerMethodField()
    avarege_price = serializers.SerializerMethodField()
    group_plan = GroupPlansSerializer()
    user = serializers.PrimaryKeyRelatedField(queryset=User.objects.all(), required=False, many=False)

    class Meta:
        model = Plan
        fields = '__all__'

    def get_number_reservations(self, plan):
        return plan.number_room

    def get_reservation_amount(self, plan):
        return plan.total_fee

    def get_avarege_price(self, plan):
        return plan.total_fee / plan.number_room if plan.number_room else 0


class PlanUpdateGroupPlanIdSerializer(serializers.ModelSerializer):
    class Meta:
        model = Plan
        # Include any other fields you want to update
        fields = ['group_plan_id']


class PlanCSVImportSerializer(serializers.Serializer):
    csv_file = serializers.FileField(help_text="CSV file to import")
    headers = serializers.ListField(help_text="header object")

class CustomHeadersImportSerializer(serializers.Serializer):
    csv_file = serializers.FileField(help_text="CSV file to import")


class GetPlansSerializer(serializers.Serializer):
    ids = serializers.ListField(child=serializers.IntegerField())


class PlanPartialUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Plan
        fields = [
            'group_plan',
            'id'
        ]
