# Generated by Django 4.2.5 on 2023-10-22 12:57

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Plan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('plan_group_name', models.CharField(max_length=255, verbose_name='プラングループ名')),
                ('sales_period', models.DateField(verbose_name='販売期間')),
                ('estimated_reservation_count', models.IntegerField(verbose_name='予約件数')),
                ('reservation_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='予約金額')),
                ('average_unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='平均単価')),
            ],
            options={
                'db_table': 'plans',
            },
        ),
    ]
