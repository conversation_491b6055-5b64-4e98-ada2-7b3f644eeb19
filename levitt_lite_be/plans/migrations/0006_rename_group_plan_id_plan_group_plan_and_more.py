# Generated by Django 4.2.6 on 2023-10-27 10:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('plans', '0005_rename_group_plan_plan_group_plan_id'),
    ]

    operations = [
        migrations.RenameField(
            model_name='plan',
            old_name='group_plan_id',
            new_name='group_plan',
        ),
        migrations.RemoveField(
            model_name='plan',
            name='average_unit_price',
        ),
        migrations.RemoveField(
            model_name='plan',
            name='reservation_amount',
        ),
        migrations.RemoveField(
            model_name='plan',
            name='sales_end_time',
        ),
        migrations.RemoveField(
            model_name='plan',
            name='sales_start_time',
        ),
        migrations.RemoveField(
            model_name='plan',
            name='stay_end_time',
        ),
        migrations.RemoveField(
            model_name='plan',
            name='stay_start_time',
        ),
        migrations.AddField(
            model_name='plan',
            name='benefits',
            field=models.TextField(null=True, verbose_name='特典'),
        ),
        migrations.AddField(
            model_name='plan',
            name='checkin_time',
            field=models.DateField(null=True, verbose_name='チェックイン日'),
        ),
        migrations.AddField(
            model_name='plan',
            name='checkout_time',
            field=models.DateField(null=True, verbose_name='チェックアウト日'),
        ),
        migrations.AddField(
            model_name='plan',
            name='number_room',
            field=models.IntegerField(null=True, verbose_name='泊数'),
        ),
        migrations.AddField(
            model_name='plan',
            name='plan_description',
            field=models.TextField(null=True, verbose_name='プラン説明文'),
        ),
        migrations.AddField(
            model_name='plan',
            name='reservation_site',
            field=models.TextField(null=True, verbose_name='予約サイト名称'),
        ),
        migrations.AddField(
            model_name='plan',
            name='reservation_time',
            field=models.DateField(null=True, verbose_name='申込日'),
        ),
        migrations.AddField(
            model_name='plan',
            name='room_type',
            field=models.CharField(max_length=255, null=True, verbose_name='部屋タイプ名称'),
        ),
        migrations.AddField(
            model_name='plan',
            name='total_fee',
            field=models.IntegerField(null=True, verbose_name='料金合計額'),
        ),
        migrations.AlterField(
            model_name='plan',
            name='name',
            field=models.TextField(null=True, verbose_name='プラングループ名'),
        ),
    ]
