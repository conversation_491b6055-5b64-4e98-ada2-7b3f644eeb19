from datetime import datetime
import re

def parse_date(date_str):
    date_formats = [
        (r'\d{4}/\d{2}/\d{2}', '%Y/%m/%d'),  # YYYY/MM/DD
        (r'\d{4}-\d{2}-\d{2}', '%Y-%m-%d'),  # YYYY-MM-DD
        (r'\d{4}\d{2}\d{2}', '%Y%m%d'),  # YYYYMMDD
        (r'\d{1,2}/\d{1,2}/\d{4}', '%m/%d/%Y'),  # M/D/YYYY
        (r'\d{2}/\d{2}/\d{4}', '%d/%m/%Y'),  # DD/MM/YYYY
        (r'\d{1,2}/\d{1,2}/\d{4}', '%d/%m/%Y'),  # D/M/YYYY
        (r'\d{2}/\d{2}/\d{4}', '%m/%d/%Y'),  # MM/DD/YYYY
        (r'\d{4}/\d{1,2}/\d{1,2}', '%Y/%m/%d'),  # YYYY/M/D
        (r'\d{4}-\d{1,2}-\d{1,2}', '%Y-%m-%d'),  # YYYY-M-D
        (r'\d{4}\d{1,2}\d{1,2}', '%Y%m%d'),  # YYYYMD
        (r'\d{8}', '%Y%m%d'),  # YYYYMMDD without separators
    ]

    datetime_formats = [
        (r'\d{4}/\d{2}/\d{2} \d{2}:\d{2}:\d{2}', '%Y/%m/%d %H:%M:%S'),  # YYYY/MM/DD HH:MM:SS
        (r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}', '%Y-%m-%d %H:%M:%S'),  # YYYY-MM-DD HH:MM:SS
        (r'\d{4}\d{2}\d{2} \d{2}:\d{2}:\d{2}', '%Y%m%d %H:%M:%S'),  # YYYYMMDD HH:MM:SS
        (r'\d{4}/\d{2}/\d{2} \d{2}:\d{2}', '%Y/%m/%d %H:%M'),  # YYYY/MM/DD HH:MM
        (r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}', '%Y-%m-%d %H:%M'),  # YYYY-MM-DD HH:MM
        (r'\d{4}\d{2}\d{2} \d{2}:\d{2}', '%Y%m%d %H:%M'),  # YYYYMMDD HH:MM
        (r'\d{4}/\d{1,2}/\d{1,2} \d{1,2}:\d{2}', '%Y/%m/%d %H:%M'),  # YYYY/M/D H:MM
        (r'\d{4}-\d{1,2}-\d{1,2} \d{1,2}:\d{2}', '%Y-%m-%d %H:%M'),  # YYYY-M-D H:MM
        (r'\d{4}\d{1,2}\d{1,2} \d{1,2}:\d{2}', '%Y%m%d %H:%M'),  # YYYYMD H:MM
    ]

    for pattern, format in datetime_formats + date_formats:
        if re.fullmatch(pattern, date_str):
            try:
                return datetime.strptime(date_str, format).date()
            except ValueError:
                continue

    raise ValueError("サポートされていない日付形式です")
