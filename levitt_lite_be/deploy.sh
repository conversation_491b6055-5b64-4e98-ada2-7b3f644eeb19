# Exit when any command fails
set -e

if [ -z "$1" ]; then
  echo "Error: Fill github branch at first parmeter, ex. sh deploy.sh dev"
  exit 0
fi

# Pull new source code
echo "Pulling new source code..."
git fetch origin $1
git checkout -f
git checkout $1
git pull origin $1

echo "Activating python virtual environment..."
# Config environment
. ./venv/bin/activate

echo "Installing dependencies..."
pip install -r requirements.txt
python manage.py makemigrations
python manage.py migrate

# Start server as a daemon process (runing in background)
uwsgi --ini uwsgi.ini --daemonize uwsgi.log

echo "Reloading uwsgi..."
uwsgi --reload running_app.pid

# Load env for secret variable
if [ -f .env ]; then
  export $(echo $(cat .env | sed 's/#.*//g'| xargs) | envsubst)
fi

curl -X POST --data-urlencode "payload={\"channel\": \"#$SLACK_CHANNEL\", \"username\": \"Bot\", \"text\": \"@channel [$SLACK_CHANNEL] Server $APP_URL has been successfully deployed!\", \"icon_emoji\": \":ghost:\"}" $SLACK_PUSH_CHANNEL
echo "\nDeploy done."
exit 0
