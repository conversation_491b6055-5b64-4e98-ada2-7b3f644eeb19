"""
System prompts for OpenAI API calls
"""
from .gpt_plan.scraping import Scraping_Rakuten_data
import logging

logger = logging.getLogger(__name__)

# OpenAI API Configuration
def get_openai_config():
    """Get OpenAI API configuration parameters"""
    return {
        "model": "gpt-4.1",
        "max_tokens": 8192,      # Sufficient for hotel plan responses
        "temperature": 0.3,      # Balanced creativity and consistency
        "timeout": 300          # 5 minutes timeout
    }

def get_hotel_plan_system_prompt():
    """Get the system prompt for hotel plan creation"""
    prompt = (
        "You are a professional hotel planner. You are tasked with creating optimal hotel plans in the specified format. "
        "Please incorporate seasonal themes if a time period is entered. Reference the attached file for guidance.\n\n"
        "# Task\n\n"
        "- Reflect seasonal themes in descriptions if a period is specified.\n"
        "- Provide a restaurant description in \"meal\" if breakfast or dinner is included.\n"
        "- Ensure all fields sound enjoyable and are written in Japanese.\n"
        "- Follow the specified Python dictionary format for the response.\n\n"
        "# Format Requirements\n\n"
        "Respond in Japanese according to the following format in Python dict:\n"
        "```markdown\n"
        "{\n"
        "    \"title\": \" \",\n"
        "    \"description\": \" \",\n"
        "    \"meal_description\": \" \",\n"
        "    \"hotel_concept\": \" \",\n"
        "    \"coution\": \" \",\n"
        "    \"benefits\": \" \"\n"
        "}\n"
        "```\n\n"
        "# Data Creation Guidelines\n\n"
        "- **title**: Up to 50 characters. Ensure clarity, distinctiveness, appeal to the target audience, and memorability. "
        "Consider using symbols like 【】, ！, 〜.\n"
        "- **description**: Provide an appealing narrative that highlights selling points and stay imagery for the target audience.\n"
        "- **meal_description**: Detail the meal offerings to set them apart from other facilities.\n"
        "- **hotel_concept**: Highlight each facility's strengths and unique aspects to facilitate comparison during booking.\n"
        "- **benefits**: List user merits in bullet points. Do not include this field if no benefits are present in the input.\n\n"
        "# Notes\n\n"
        "- Reflect the content to be appealing and joyous.\n"
        "- Ensure clear differentiation from other facilities in your descriptions.\n"
        "- Return ONLY the JSON dictionary without any additional text or formatting."
    )
    return prompt

def create_user_prompt(parameta: dict) -> str:
    """Create user prompt for OpenAI API based on user input and hotel data"""
    try:
        # Get hotel information if hotel_id is provided
        hotel_data = {}
        if 'hotel_id' in parameta:
            try:
                scraped_data = Scraping_Rakuten_data()
                hotel_data = scraped_data.get_info(parameta['hotel_id'])
                logger.info(f"Successfully scraped hotel data for hotel_id: {parameta['hotel_id']}")
            except Exception as e:
                logger.warning(f"Failed to get hotel data: {e}")
                # Continue without hotel data

        # Build the base prompt message
        base_conditions = [
            f"[期間]: {parameta.get('start_tarm', 'N/A')}~{parameta.get('end_tarm', 'N/A')}",
            f"客室タイプ: {parameta.get('room_type', 'N/A')}",
            f"宿泊日の種別: {parameta.get('date_type', 'N/A')}",
            f"朝食の有無: {parameta.get('meal', 'N/A')}",
            f"特典: {parameta.get('benefits', 'N/A')}",
            f"入力特典: {parameta.get('input_perks', 'N/A')}"
        ]

        prompt_message = "では、以下の条件を踏まえてプランを生成してください\n" + "\n".join(base_conditions)

        # Add hotel information if available
        if hotel_data:
            hotel_info = [
                f"ホテル名: {hotel_data.get('hotel_name', 'N/A')}",
                f"住所: {hotel_data.get('address', 'N/A')}",
                f"交通アクセス: {hotel_data.get('access', 'N/A')}",
                f"館内設備: {hotel_data.get('hotel_facility', 'N/A')}",
                f"部屋設備・備品: {hotel_data.get('room_facility', 'N/A')}",
                f"チェックイン: {hotel_data.get('check_in', 'N/A')}",
                f"チェックアウト: {hotel_data.get('check_out', 'N/A')}",
                f"条件・注意事項: {hotel_data.get('coution', 'N/A')}"
            ]

            prompt_message += "\n\nホテル情報:\n" + "\n".join(hotel_info)

        logger.info(f"Created user prompt with length: {len(prompt_message)}")
        return prompt_message

    except Exception as e:
        logger.error(f"Error creating user prompt: {e}")
        raise Exception(f"Failed to create user prompt: {e}")
