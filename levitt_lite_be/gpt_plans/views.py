import json
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.pagination import PageNumberPagination
from .serializers import RecommendPlanSerializer
from .models import RecommendPlan
from hotels.models import Hotels
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from users.views import IsTokenValid, IsAuthenticated
from .tasks import create_plan_async
from celery.result import AsyncResult
from users.mixins import ApiCallLimitMixin
import logging

logger = logging.getLogger(__name__)


class CustomPaginationClassRecommandPlan(PageNumberPagination):
    page_size = 20
    page_size_query_param = 'page_size'


class CreateGPTPlanAsyncView(APIView, ApiCallLimitMixin):
    permission_classes = [IsTokenValid, IsAuthenticated]

    @swagger_auto_schema(
        request_body=openapi.Schema(type=openapi.TYPE_OBJECT,
            properties={
                'start_tarm': openapi.Schema(type=openapi.TYPE_STRING, description='2023-12-30'),
                'end_tarm': openapi.Schema(type=openapi.TYPE_STRING, description='2024-02-24'),
                'meal': openapi.Schema(type=openapi.TYPE_STRING, description='朝食付き'),
                'benefits': openapi.Schema(type=openapi.TYPE_STRING, description='飲み放題'),
                'room_type': openapi.Schema(type=openapi.TYPE_STRING, description='ツイン'),
                'date_type': openapi.Schema(type=openapi.TYPE_STRING, description='平日'),
                'hotel_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='38574'),
            },
            required=['start_tarm', 'end_tarm', 'meal', 'benefits', 'room_type', 'date_type', 'hotel_id']
        )
    )
    def post(self, request):
        try:
            # Check API call limit
            limit_response = self.check_api_call_limit(request)
            if limit_response:
                return limit_response

            if not request.data:
                return Response({
                    "message": "No data provided",
                    "status": status.HTTP_400_BAD_REQUEST,
                }, status=status.HTTP_400_BAD_REQUEST)

            # Validate and extract input data
            user_input_dict = {}
            required_fields = ['room_type', 'meal', 'date_type']

            for field in required_fields:
                if field not in request.data:
                    return Response({
                        "message": f"Missing required field: {field}",
                        "status": status.HTTP_400_BAD_REQUEST,
                    }, status=status.HTTP_400_BAD_REQUEST)
                user_input_dict[field] = request.data[field]

            # Optional fields
            optional_fields = ['hotel_id', 'benefits', 'start_tarm', 'end_tarm']
            for field in optional_fields:
                if field in request.data:
                    user_input_dict[field] = request.data[field]

            logger.info(f"Creating plan task for user {request.user.id} with data: {user_input_dict}")

            # Create async task
            task = create_plan_async.delay(user_input_dict)
            task_id = task.id

            logger.info(f"Plan creation task created: {task_id}")

            return Response({
                "message": "Task scheduled successfully",
                "data": {"task_id": task_id},
                "status": status.HTTP_202_ACCEPTED,
            }, status=status.HTTP_202_ACCEPTED)

        except Exception as e:
            logger.error(f"Error creating plan task: {e}")
            return Response({
                "message": "Internal server error occurred",
                "status": status.HTTP_500_INTERNAL_SERVER_ERROR,
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class GetGPTPlanAsyncView(APIView):
    permission_classes = [IsTokenValid, IsAuthenticated]

    def get(self, request, task_id):
        try:
            if not task_id or task_id == 'undefined':
                return Response({
                    "message": "Invalid task_id",
                    "status": status.HTTP_400_BAD_REQUEST,
                }, status=status.HTTP_400_BAD_REQUEST)

            logger.info(f"Checking task status: {task_id}")
            task_result = AsyncResult(task_id)

            if task_result.ready():
                try:
                    result = task_result.get()

                    # Check if result is an error
                    if isinstance(result, str):
                        try:
                            result_dict = json.loads(result)
                            if isinstance(result_dict, dict) and result_dict.get('error'):
                                logger.error(f"Task {task_id} failed: {result_dict.get('message')}")
                                return Response({
                                    "message": result_dict.get('message', 'Task failed'),
                                    "status": status.HTTP_500_INTERNAL_SERVER_ERROR,
                                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                        except json.JSONDecodeError:
                            pass  # Continue with normal processing

                    # Task completed successfully
                    user = request.user
                    user_profile = user.users
                    user_profile.used_time += 1
                    user_profile.save()

                    logger.info(f"Task {task_id} completed successfully")
                    return Response({
                        "message": "Get task's data successfully",
                        "data": result,
                        "status": status.HTTP_200_OK,
                    }, status=status.HTTP_200_OK)

                except Exception as e:
                    logger.error(f"Error getting task result {task_id}: {e}")
                    return Response({
                        "message": "Error retrieving task result",
                        "status": status.HTTP_500_INTERNAL_SERVER_ERROR,
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            else:
                # Task still pending
                task_state = task_result.state
                logger.debug(f"Task {task_id} still pending, state: {task_state}")

                return Response({
                    "message": "Pending",
                    "data": {"state": task_state},
                    "status": "pending",
                }, status=status.HTTP_202_ACCEPTED)

        except Exception as e:
            logger.error(f"Error checking task {task_id}: {e}")
            return Response({
                "message": "Internal server error occurred",
                "status": status.HTTP_500_INTERNAL_SERVER_ERROR,
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ResistGPTPlanView(APIView):
    permission_classes = [IsTokenValid, IsAuthenticated]

    def post(self, request):
        # 受け取ったjson内のhotel_nameに一致するDB内のhotel_idを取得
        # 外部キーとしてgpt_plan_testに保存するもの
        user = request.user
        hotel_id_dict = {}
        if 'hotel_name' in request.data:
            try:
                hotel_name = request.data["hotel_name"]
                db_hotel_name = Hotels.objects.filter(name=hotel_name)
                hotel = db_hotel_name.first()

                if hotel:
                    hotel_id_dict = {
                        'hotel': hotel.id
                    }
                else:
                    hotel_id_dict = {}

            except Exception as e:
                hotel_id_dict = {}

        insert_dict = {**request.data, **hotel_id_dict}
        insert_dict['user'] = user.id

        serializer = RecommendPlanSerializer(data=insert_dict)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ListAllGPTPlansView(APIView):
    def get(self, request):
        try:
            gpt_plans = RecommendPlan.objects.all()
            serializer = RecommendPlanSerializer(gpt_plans, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Error listing all GPT plans: {e}")
            return Response({
                "message": "Internal server error occurred",
                "status": status.HTTP_500_INTERNAL_SERVER_ERROR
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ListGPTPlansView(APIView):
    permission_classes = [IsTokenValid, IsAuthenticated]
    def get(self, request):
        try:
            user = request.user
            query = RecommendPlan.objects.filter(user=user)
            serializer = RecommendPlanSerializer(query, many=True)
            return Response(serializer.data)
        except Exception as e:
            logger.error(f"Error listing user GPT plans: {e}")
            return Response({
                "message": "Internal server error occurred",
                "status": status.HTTP_500_INTERNAL_SERVER_ERROR
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class FetchGPTPlansView(APIView):
    def post(self, request):
        try:
            plan_id = request.data.get('id')
            if not plan_id:
                return Response({
                    "message": "Plan ID is required",
                    "status": status.HTTP_400_BAD_REQUEST
                }, status=status.HTTP_400_BAD_REQUEST)

            query = RecommendPlan.objects.filter(id=plan_id)
            serializer = RecommendPlanSerializer(query, many=True)
            return Response(serializer.data)
        except Exception as e:
            logger.error(f"Error fetching GPT plan: {e}")
            return Response({
                "message": "Internal server error occurred",
                "status": status.HTTP_500_INTERNAL_SERVER_ERROR
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
