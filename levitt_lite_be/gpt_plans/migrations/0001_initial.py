# Generated by Django 4.2.7 on 2023-12-07 07:16

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('hotels', '0021_competitionregistration_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='GPTPlans',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('room_type', models.CharField(max_length=255, null=True, verbose_name='部屋タイプ名称')),
                ('title', models.TextField(max_length=63, verbose_name='タイトル')),
                ('desription', models.TextField(verbose_name='プラン説明文')),
                ('benefits', models.TextField(null=True, verbose_name='特典')),
                ('sales_start_period', models.DateField(null=True, verbose_name='販売開始期間')),
                ('sales_end_period', models.DateField(null=True, verbose_name='販売終了期間')),
                ('checkin_time', models.TimeField(null=True, verbose_name='チェックイン時間')),
                ('checkout_time', models.TimeField(null=True, verbose_name='チェックアウト時間')),
                ('coution', models.TextField(verbose_name='注意事項')),
                ('meal_type', models.CharField(max_length=63, verbose_name='食事')),
                ('meal_description', models.TextField(null=True, verbose_name='食事の説明')),
                ('hotel_facility', models.TextField(null=True, verbose_name='館内設備')),
                ('room_facility', models.TextField(null=True, verbose_name='室内設備')),
                ('hotel_concept', models.TextField(null=True, verbose_name='ホテルのコンセプト')),
                ('access', models.TextField(null=True, verbose_name='交通アクセス')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='作成日')),
                ('hotel', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='gpt_plans_related', to='hotels.hotels')),
            ],
            options={
                'db_table': 'gpt_plan_test',
            },
        ),
    ]
