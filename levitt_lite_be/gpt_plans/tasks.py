from __future__ import absolute_import, unicode_literals
from celery import shared_task
from .prompts import get_hotel_plan_system_prompt, create_user_prompt, get_openai_config
from .gpt_plan.scraping import Scraping_Rakuten_data
from openai import OpenAI
import os
import time
import json
from rest_framework.response import Response
from rest_framework import status
from celery.result import AsyncResult
from dotenv import load_dotenv
import logging

load_dotenv()

logger = logging.getLogger(__name__)

def convert_json_text(input_text):
    """Extract JSON content from text response"""
    if not input_text:
        return None
    json_start = input_text.find('{')
    json_end = input_text.rfind('}') + 1
    if json_start != -1 and json_end != -1:
        return input_text[json_start:json_end]
    else:
        return None

def create_plan_with_openai(client, user_prompt, max_retries=3):
    """Create plan using OpenAI Chat Completions API with retry logic"""

    # Get OpenAI configuration
    config = get_openai_config()

    for attempt in range(max_retries):
        try:
            logger.info(f"OpenAI API call attempt {attempt + 1}/{max_retries}")

            response = client.chat.completions.create(
                model=config["model"],
                messages=[
                    {"role": "system", "content": get_hotel_plan_system_prompt()},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=config["max_tokens"],
                temperature=config["temperature"],
                timeout=config["timeout"]
            )

            if response.choices and response.choices[0].message:
                content = response.choices[0].message.content
                logger.info(f"OpenAI API response received, length: {len(content)}")

                # Extract JSON from response
                json_content = convert_json_text(content)
                if json_content:
                    try:
                        plan_data = json.loads(json_content, strict=False)
                        logger.info("Successfully parsed JSON response")
                        return plan_data
                    except json.JSONDecodeError as e:
                        logger.error(f"JSON decode error: {e}")
                        if attempt == max_retries - 1:
                            raise Exception(f"Invalid JSON in response: {e}")
                else:
                    logger.error("No valid JSON found in response")
                    if attempt == max_retries - 1:
                        raise Exception("No valid JSON found in OpenAI response")
            else:
                logger.error("Empty response from OpenAI API")
                if attempt == max_retries - 1:
                    raise Exception("Empty response from OpenAI API")

        except Exception as e:
            logger.error(f"OpenAI API error on attempt {attempt + 1}: {e}")
            if attempt == max_retries - 1:
                raise Exception(f"OpenAI API failed after {max_retries} attempts: {e}")

            # Wait before retry with exponential backoff
            wait_time = (2 ** attempt) + 1
            logger.info(f"Waiting {wait_time} seconds before retry...")
            time.sleep(wait_time)

    raise Exception("Failed to get valid response from OpenAI API")

@shared_task(bind=True, max_retries=2)
def create_plan_async(self, user_input_kwargs: dict):
    """Create plan using OpenAI Chat Completions API with improved error handling"""

    try:
        logger.info(f"Starting plan creation task: {self.request.id}")

        # Validate input
        required_fields = ['room_type', 'meal', 'date_type']
        for field in required_fields:
            if field not in user_input_kwargs:
                raise ValueError(f"Missing required field: {field}")

        # Create OpenAI client
        client = OpenAI(api_key=os.environ['OPENAI_API_KEY'])

        # Create user prompt
        user_prompt = create_user_prompt(parameta=user_input_kwargs)
        logger.info(f"Created user prompt, length: {len(user_prompt)}")

        # Get plan data from OpenAI
        plan_data_dict = create_plan_with_openai(client, user_prompt)

        # Process hotel data if hotel_id provided
        hotel_data_dict = {
            'room_type': user_input_kwargs['room_type'],
            'meal_type': user_input_kwargs['meal'],
        }

        if 'hotel_id' in user_input_kwargs:
            try:
                scraped_data = Scraping_Rakuten_data().get_info(user_input_kwargs['hotel_id'])
                hotel_data_dict.update({
                    'hotel_name': scraped_data.get('hotel_name', ''),
                    'checkin_time': scraped_data.get('check_in', ''),
                    'checkout_time': scraped_data.get('check_out', ''),
                    'hotel_facility': scraped_data.get('hotel_facility', ''),
                    'room_facility': scraped_data.get('room_facility', ''),
                    'access': scraped_data.get('access', ''),
                })
                hotel_data_dict['hotel_id'] = int(user_input_kwargs['hotel_id'])
                logger.info(f"Scraped hotel data for hotel_id: {user_input_kwargs['hotel_id']}")
            except Exception as e:
                logger.warning(f"Failed to scrape hotel data: {e}")
                # Continue without hotel data

        if 'start_tarm' in user_input_kwargs and 'end_tarm' in user_input_kwargs:
            hotel_data_dict['start_tarm'] = user_input_kwargs['start_tarm']
            hotel_data_dict['end_tarm'] = user_input_kwargs['end_tarm']

        # Combine all data
        create_plan_dict = {**plan_data_dict, **hotel_data_dict}

        logger.info(f"Plan creation completed successfully for task: {self.request.id}")
        return json.dumps(create_plan_dict, ensure_ascii=False)

    except Exception as e:
        logger.error(f"Plan creation failed for task {self.request.id}: {e}")

        # Retry logic with exponential backoff
        if self.request.retries < self.max_retries:
            retry_delay = 60 * (2 ** self.request.retries)  # 60s, 120s, 240s
            logger.info(f"Retrying task {self.request.id}, attempt {self.request.retries + 1}")
            raise self.retry(countdown=retry_delay, exc=e)
        else:
            logger.error(f"Task {self.request.id} failed after all retries")
            raise e

@shared_task
def get_pran_async(self, requet, task_id):
        task_result = AsyncResult(task_id)

        print(task_result.status)

        if task_result.ready():
            return Response(task_result.get(), status=status.HTTP_200_OK)
        else:
            return Response({"status": "pending"}, status=status.HTTP_202_ACCEPTED)
