import requests
from bs4 import BeautifulSoup
import unicodedata
from rest_framework.response import Response
from rest_framework import status
import logging

# 楽天トラベルからスクレイピング
# get_infoメソッドを呼び出せばhotel_idに依存したデータが手に入る
class Scraping_Rakuten_data():
    BASE_URL = 'https://travel.rakuten.co.jp/HOTEL/'

    def normalize_text(self, text:str) -> str:
        text_without_tabs = text.replace('\t', '')
        return unicodedata.normalize("NFKC", text_without_tabs)

    def create_url(self, hotel_id: str) -> str:
        return f'{self.BASE_URL}{hotel_id}/{hotel_id}_std.html'


    def get_html(self, hotel_id: str) -> str:
        try:
            url = self.create_url(hotel_id)
            r = requests.get(url)
            r.raise_for_status()
            soup = BeautifulSoup(r.content, 'html.parser')
            return soup

        except requests.HTTPError as e:
            print(f"URL Error is happened while scraping hotel information: {e}")
            return Response({
                "message": "HTTP Error is happened while scraping hotel information" ,
                "status": e
            }, status=status.HTTP_404_NOT_FOUND)
        except requests.URLRequired as e:
            print(f"URL Error is happened while scraping hotel information:{e}")
            return Response({
                "message": "URL Error is happened while scraping hotel information",
                "status": e
            }, status=status.HTTP_404_NOT_FOUND)
        except requests.RequestException as e:
            print(f"An error occurred: {e}")
            return Response(f"An error occurred: {e}")

    def get_info(self, hotel_id: str) -> dict:
        soup = self.get_html(hotel_id)
        data = {}
        if soup is None:
            return data
        list_item = soup.find_all('li')
        all_li_info = {}

        for item in list_item:
            dt_element = item.find('dt')
            dd_element = item.find('dd')
            if dt_element and dd_element:
                dt_text = dt_element.text.strip()
                dd_text = dd_element.text.strip()
                all_li_info[dt_text] = dd_text

        # Safe handling of check-in time
        checkin_info = all_li_info.get('チェックイン', '')
        if checkin_info:
            check_in = checkin_info[0:5] if len(checkin_info) >= 5 else checkin_info
            # 最終チェックインが書かれていない場合の処理
            if len(checkin_info) > 5:
                last_check_in = checkin_info[-6:-1]
            else:
                last_check_in = None
        else:
            check_in = ''
            last_check_in = None

        # Safe data extraction with default values
        data['hotel_id']       = int(hotel_id)

        # Hotel name from h2 tag
        hotel_name_elem = soup.find("h2")
        data['hotel_name']     = self.normalize_text(hotel_name_elem.getText().strip()) if hotel_name_elem else ''

        # Other fields with safe handling
        data['address']        = self.normalize_text(all_li_info.get('住所', ''))
        data['access']         = self.normalize_text(all_li_info.get('交通アクセス', ''))
        data['hotel_facility'] = self.normalize_text(all_li_info.get('館内設備', ''))
        data['room_facility']  = self.normalize_text(all_li_info.get('部屋設備・備品', ''))
        data['check_in']       = self.normalize_text(check_in)
        data['last_check_in']  = last_check_in
        data['check_out']      = self.normalize_text(all_li_info.get('チェックアウト', ''))
        # Safe concatenation to handle None values
        coution_parts = [
            all_li_info.get('条件・注意事項', ''),
            all_li_info.get('キャンセルポリシー', ''),
            all_li_info.get('連絡なしの不泊について', '')
        ]
        coution_text = ''.join(part for part in coution_parts if part)
        data['coution'] = self.normalize_text(coution_text) if coution_text else ''

        return data