from django.db import models
from hotels.models import Hotels
from django.contrib.auth.models import User


class RecommendPlan(models.Model):
    room_type = models.CharField(max_length=255, null=True, verbose_name='部屋タイプ名称')
    title = models.TextField(max_length=63, null=True, verbose_name='タイトル')
    description = models.TextField(null=True, verbose_name='プラン説明文')
    benefits = models.TextField(null=True, verbose_name='特典')
    sales_start_period = models.DateField(null=True, verbose_name='販売開始期間')
    sales_end_period = models.DateField(null=True, verbose_name='販売終了期間')
    checkin_time = models.TimeField(null=True, verbose_name='チェックイン時間')
    checkout_time = models.TimeField(null=True, verbose_name='チェックアウト時間')
    caution = models.TextField(null=True, verbose_name='注意事項')
    meal_type = models.CharField(null=True, max_length=63, verbose_name='食事')
    meal_description = models.TextField(null=True, verbose_name='食事の説明')
    hotel_facility = models.TextField(null=True, verbose_name='館内設備')
    room_facility = models.TextField(null=True, verbose_name='室内設備')
    hotel_concept = models.TextField(null=True, verbose_name='ホテルのコンセプト')
    access = models.TextField(null=True, verbose_name='交通アクセス')
    hotel = models.ForeignKey(Hotels, on_delete=models.DO_NOTHING, related_name='gpt_plans_related', null=True, blank=True)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='recommend_plans')
    created_at = models.DateTimeField(verbose_name='作成日', auto_now_add = True)

    class Meta:
        db_table = 'recommend_plans'
