# 📋 AI プラン作成フローの詳細

## 🎯 システム概要

プラン作成機能は非同期アーキテクチャを使用し、以下の主要コンポーネントで構成されています：

- **フロントエンド**: React.js with Redux Toolkit
- **バックエンド**: Django REST Framework with Celery
- **AI エンジン**: OpenAI GPT-4o-mini
- **タスクキュー**: Celery with Redis
- **データソース**: 楽天からのホテルデータスクレイピング

---

## 🎯 1. フロントエンド - プラン作成画面 (`/plan/create`)

### 1.1 UI とフォームバリデーション

**ファイル**: `levitt_lite_fe/src/pages/plan/create/index.jsx`

**主な入力フィールド:**

- `room_type`: 部屋タイプ (ツイン、シングル等)
- `meal`: 食事 (朝食付き、夕食付き等)
- `date_type`: 日付タイプ (平日、土日等)
- `benefits`: 特典 (飲み放題等)
- `hotel_id`: ホテルID
- `start_tarm` & `end_tarm`: 宿泊期間
- `input_perks`: カスタム特典

**バリデーションスキーマ:**

```javascript
validationSchema: () => {
  return Yup.object({
    room_type: Yup.string().required(TEXT_REQUIRED),
    date_type: Yup.string().required(TEXT_REQUIRED),
    meal: Yup.string().required(TEXT_REQUIRED),
    benefits: Yup.string().required(TEXT_REQUIRED),
    hotel_id: Yup.mixed().required(TEXT_REQUIRED),
    input_perks: Yup.string().when("benefits", {
      is: (benefits) => benefits === "その他",
      then: (schema) => schema.required(TEXT_REQUIRED),
    }),
    start_tarm: Yup.mixed().when("date_select_type", {
      is: false,
      then: (schema) => schema.required(TEXT_REQUIRED),
    }),
    end_tarm: Yup.mixed().when("date_select_type", {
      is: false,
      then: (schema) => schema.required(TEXT_REQUIRED),
    }),
  });
}
```

### 1.2 フォーム送信処理

```javascript
onSubmit: async (values, { setSubmitting, setErrors }) => {
  setSubmitting(true);
  
  // データフォーマット
  const copyValues = { ...values };
  if (copyValues.date_select_type) {
    delete copyValues.start_tarm;
    delete copyValues.end_tarm;
  } else {
    copyValues.start_tarm = moment(copyValues.start_tarm).format("YYYY-MM-DD");
    copyValues.end_tarm = moment(copyValues.end_tarm).format("YYYY-MM-DD");
  }
  
  copyValues.hotel_id = values.hotel_id?.in_site_id;
  
  // 再利用のためにリクエストをlocalStorageに保存
  localStorage.setItem("planRequested", JSON.stringify(copyValues));
  
  delete copyValues.date_select_type;
  
  // プラン作成APIを呼び出し
  await handleCreatePlan(copyValues);
  setSubmitting(false);
}
```

---

## 🔄 2. フロントエンド - Redux Action (`createPlan`)

### 2.1 Redux Thunk Action

**ファイル**: `levitt_lite_fe/src/features/plans/plansSlice.js`

```javascript
export const createPlan = createAsyncThunk(
  "plans/createPlan", 
  async (body, { rejectWithValue }) => {
    try {
      const response = await HTTP.post(`/api/gpt_plans/create/asy/`, body);
      return response;
    } catch (error) {
      // 日本語エラーメッセージによる詳細なエラー処理
      if (error.response) {
        const statusCode = error.response.status;
        
        if (statusCode === 429) {
          return rejectWithValue({
            statusCode: 429,
            message: "API呼び出し制限に達しました。しばらくしてから再試行してください。"
          });
        } else if (statusCode === 400) {
          return rejectWithValue({
            statusCode: 400,
            message: "リクエストデータが無効です。入力内容を確認してください。"
          });
        } else if (statusCode === 401) {
          return rejectWithValue({
            statusCode: 401,
            message: "認証に失敗しました。再度ログインしてください。"
          });
        } else if (statusCode === 500) {
          return rejectWithValue({
            statusCode: 500,
            message: "サーバーエラーが発生しました。再試行してください。"
          });
        }
      } else if (error.request) {
        // ネットワークエラー
        return rejectWithValue({
          statusCode: 0,
          message: "ネットワークエラーです。接続を確認してください。"
        });
      } else {
        // その他のエラー
        return rejectWithValue({
          statusCode: 0,
          message: "予期しないエラーが発生しました。"
        });
      }
    }
  }
);
```

### 2.2 Redux 状態管理

```javascript
extraReducers: {
  [createPlan.pending]: (state) => {
    state.loading = true;
    state.error = null;
  },
  [createPlan.fulfilled]: (state, { payload }) => {
    state.loading = false;
    if (payload.error) {
      state.error = payload.error;
    }
  },
  [createPlan.rejected]: (state, { payload, error }) => {
    state.loading = false;
    if (payload) {
      state.error = payload.message || payload;
    } else {
      state.error = error.message || "An error occurred";
    }
  },
}
```

---

## 🚀 3. バックエンド - API エンドポイント (`/api/gpt_plans/create/asy/`)

### 3.1 View ハンドラー

**ファイル**: `levitt_lite_be/gpt_plans/views.py`

```python
class CreateGPTPlanAsyncView(APIView, ApiCallLimitMixin):
    permission_classes = [IsTokenValid, IsAuthenticated]

    def post(self, request):
        try:
            # 1. API呼び出し制限チェック
            limit_response = self.check_api_call_limit(request)
            if limit_response:
                return limit_response  # 制限超過時は429を返す

            if not request.data:
                return Response({
                    "message": "No data provided",
                    "status": status.HTTP_400_BAD_REQUEST,
                }, status=status.HTTP_400_BAD_REQUEST)

            # 2. 入力データの検証
            user_input_dict = {}
            required_fields = ['room_type', 'meal', 'date_type']

            for field in required_fields:
                if field not in request.data:
                    return Response({
                        "message": f"Missing required field: {field}",
                        "status": status.HTTP_400_BAD_REQUEST,
                    }, status=status.HTTP_400_BAD_REQUEST)
                user_input_dict[field] = request.data[field]

            # オプションフィールド
            optional_fields = ['hotel_id', 'benefits', 'start_tarm', 'end_tarm']
            for field in optional_fields:
                if field in request.data:
                    user_input_dict[field] = request.data[field]

            logger.info(f"ユーザー {request.user.id} のプランタスク作成中")

            # 3. Celeryで非同期タスクを作成
            task = create_plan_async.delay(user_input_dict)
            task_id = task.id

            logger.info(f"プラン作成タスク作成完了: {task_id}")

            # 4. フロントエンドポーリング用のtask_idを返す
            return Response({
                "message": "Task scheduled successfully",
                "data": {"task_id": task_id},
                "status": status.HTTP_202_ACCEPTED,
            }, status=status.HTTP_202_ACCEPTED)

        except Exception as e:
            logger.error(f"プランタスク作成エラー: {e}")
            return Response({
                "message": "Internal server error occurred",
                "status": status.HTTP_500_INTERNAL_SERVER_ERROR,
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
```

### 3.2 API呼び出し制限 Mixin

```python
class ApiCallLimitMixin:
    def check_api_call_limit(self, request):
        user_profile = request.user.users
        if user_profile.used_time >= user_profile.max_used_time:
            return Response({
                "message": "API call limit exceeded",
                "status": status.HTTP_429_TOO_MANY_REQUESTS
            }, status=status.HTTP_429_TOO_MANY_REQUESTS)
        return None
```

---

## ⚡ 4. バックエンド - Celery タスク処理

### 4.1 非同期タスクハンドラー

**ファイル**: `levitt_lite_be/gpt_plans/tasks.py`

```python
@shared_task(bind=True, max_retries=2)
def create_plan_async(self, user_input_kwargs: dict):
    """改良されたエラーハンドリングを使用したOpenAI Chat Completions APIでのプラン作成"""
    
    try:
        logger.info(f"プラン作成タスク開始: {self.request.id}")

        # 1. 入力検証
        required_fields = ['room_type', 'meal', 'date_type']
        for field in required_fields:
            if field not in user_input_kwargs:
                raise ValueError(f"必須フィールドが不足: {field}")

        # 2. OpenAI クライアント作成
        client = OpenAI(api_key=os.environ['OPENAI_API_KEY'])

        # 3. ユーザープロンプト作成
        user_prompt = create_user_prompt(parameta=user_input_kwargs)
        logger.info(f"ユーザープロンプト作成完了、長さ: {len(user_prompt)}")

        # 4. OpenAI API呼び出し
        plan_data_dict = create_plan_with_openai(client, user_prompt)

        # 5. ホテルデータ処理
        hotel_data_dict = {
            'room_type': user_input_kwargs['room_type'],
            'meal_type': user_input_kwargs['meal'],
        }

        # 6. ホテルデータスクレイピング（hotel_idがある場合）
        if 'hotel_id' in user_input_kwargs:
            try:
                scraped_data = Scraping_Rakuten_data().get_info(user_input_kwargs['hotel_id'])
                hotel_data_dict.update({
                    'hotel_name': scraped_data.get('hotel_name', ''),
                    'checkin_time': scraped_data.get('check_in', ''),
                    'checkout_time': scraped_data.get('check_out', ''),
                    'hotel_facility': scraped_data.get('hotel_facility', ''),
                    'room_facility': scraped_data.get('room_facility', ''),
                    'access': scraped_data.get('access', ''),
                })
                hotel_data_dict['hotel_id'] = int(user_input_kwargs['hotel_id'])
                logger.info(f"hotel_id {user_input_kwargs['hotel_id']} のホテルデータスクレイピング完了")
            except Exception as e:
                logger.warning(f"ホテルデータスクレイピング失敗: {e}")

        # 7. 期間情報追加
        if 'start_tarm' in user_input_kwargs and 'end_tarm' in user_input_kwargs:
            hotel_data_dict['start_tarm'] = user_input_kwargs['start_tarm']
            hotel_data_dict['end_tarm'] = user_input_kwargs['end_tarm']

        # 8. データ結合と返却
        create_plan_dict = {**plan_data_dict, **hotel_data_dict}

        logger.info(f"タスク {self.request.id} のプラン作成が正常完了")
        return json.dumps(create_plan_dict, ensure_ascii=False)

    except Exception as e:
        logger.error(f"タスク {self.request.id} のプラン作成失敗: {e}")

        # 指数バックオフによるリトライロジック
        if self.request.retries < self.max_retries:
            retry_delay = 60 * (2 ** self.request.retries)  # 60秒、120秒、240秒
            logger.info(f"タスク {self.request.id} をリトライ中、試行回数 {self.request.retries + 1}")
            raise self.retry(countdown=retry_delay, exc=e)
        else:
            logger.error(f"タスク {self.request.id} は全てのリトライ後に失敗")
            raise e
```

### 4.2 OpenAI API 統合

```python
def create_plan_with_openai(client, user_prompt, max_retries=3):
    """リトライロジック付きOpenAI Chat Completions APIを使用したプラン作成"""
    
    # OpenAI設定取得
    config = get_openai_config()
    
    for attempt in range(max_retries):
        try:
            logger.info(f"OpenAI API呼び出し試行 {attempt + 1}/{max_retries}")

            response = client.chat.completions.create(
                model=config["model"],  # "gpt-4o-mini"
                messages=[
                    {"role": "system", "content": get_hotel_plan_system_prompt()},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=config["max_tokens"],
                temperature=config["temperature"],
                timeout=config["timeout"]
            )

            if response.choices and response.choices[0].message:
                content = response.choices[0].message.content
                logger.info(f"OpenAI APIレスポンス受信、長さ: {len(content)}")

                # レスポンスからJSON抽出
                json_content = convert_json_text(content)
                if json_content:
                    try:
                        plan_data = json.loads(json_content, strict=False)
                        logger.info("JSONレスポンスの解析成功")
                        return plan_data
                    except json.JSONDecodeError as e:
                        logger.error(f"JSONデコードエラー: {e}")
                        if attempt == max_retries - 1:
                            raise Exception(f"レスポンスの無効なJSON: {e}")
                else:
                    logger.error("レスポンスに有効なJSONが見つかりません")
                    if attempt == max_retries - 1:
                        raise Exception("OpenAIレスポンスに有効なJSONが見つかりません")
            else:
                logger.error("OpenAI APIからの空のレスポンス")
                if attempt == max_retries - 1:
                    raise Exception("OpenAI APIからの空のレスポンス")

        except Exception as e:
            logger.error(f"試行 {attempt + 1} でのOpenAI APIエラー: {e}")
            if attempt == max_retries - 1:
                raise Exception(f"{max_retries}回の試行後にOpenAI APIが失敗: {e}")

            # 指数バックオフでリトライ前の待機
            wait_time = (2 ** attempt) + 1
            logger.info(f"リトライ前に {wait_time} 秒待機...")
            time.sleep(wait_time)

    raise Exception("OpenAI APIから有効なレスポンスの取得に失敗")
```

### 4.3 プロンプトエンジニアリング

**ファイル**: `levitt_lite_be/gpt_plans/prompts.py`

プロンプトエンジニアリングは、OpenAI APIから高品質で一貫したホテルプランを生成するための重要な要素です。このセクションでは、AIに適切な指示を与えるためのプロンプト設計と設定について説明します。

#### 4.3.1 OpenAI API設定

```python
def get_openai_config():
    """OpenAI API設定パラメータを取得"""
    return {
        "model": "gpt-4o-mini",
        "max_tokens": 8192,      # ホテルプランレスポンスに十分
        "temperature": 0.3,      # 創造性と一貫性のバランス
        "timeout": 300          # 5分のタイムアウト
    }
```

**設定パラメータの詳細:**

- **`model`**: `gpt-4o-mini`を使用
  - コストパフォーマンスが高く、ホテルプラン生成に十分な性能
  - 日本語対応が優秀で、自然な文章生成が可能

- **`max_tokens`**: 8192トークン
  - ホテルプランの詳細な説明を含む完全なレスポンスに対応
  - タイトル、説明、食事情報、コンセプト等の全フィールドを生成可能

- **`temperature`**: 0.3
  - 創造性と一貫性のバランスを取った設定
  - 0に近いほど一貫性重視、1に近いほど創造性重視
  - ビジネス用途では0.2-0.4が推奨

- **`timeout`**: 300秒（5分）
  - OpenAI APIの応答待機時間
  - 複雑なプラン生成でも十分な時間を確保

#### 4.3.2 システムプロンプト設計

```python
def get_hotel_plan_system_prompt():
    """ホテルプラン作成用のシステムプロンプトを取得"""
    prompt = (
        "You are a professional hotel planner. You are tasked with creating optimal hotel plans in the specified format. "
        "Please incorporate seasonal themes if a time period is entered. Reference the attached file for guidance.\n\n"
        "# Task\n\n"
        "- Reflect seasonal themes in descriptions if a period is specified.\n"
        "- Provide a restaurant description in \"meal\" if breakfast or dinner is included.\n"
        "- Ensure all fields sound enjoyable and are written in Japanese.\n"
        "- Follow the specified Python dictionary format for the response.\n\n"
        "# Format Requirements\n\n"
        "Respond in Japanese according to the following format in Python dict:\n"
        "```markdown\n"
        "{\n"
        "    \"title\": \" \",\n"
        "    \"description\": \" \",\n"
        "    \"meal_description\": \" \",\n"
        "    \"hotel_concept\": \" \",\n"
        "    \"coution\": \" \",\n"
        "    \"benefits\": \" \"\n"
        "}\n"
        "```\n\n"
        "# Data Creation Guidelines\n\n"
        "- **title**: Up to 50 characters. Ensure clarity, distinctiveness, appeal to the target audience, and memorability. "
        "Consider using symbols like 【】, ！, 〜.\n"
        "- **description**: Provide an appealing narrative that highlights selling points and stay imagery for the target audience.\n"
        "- **meal_description**: Detail the meal offerings to set them apart from other facilities.\n"
        "- **hotel_concept**: Highlight each facility's strengths and unique aspects to facilitate comparison during booking.\n"
        "- **benefits**: List user merits in bullet points. Do not include this field if no benefits are present in the input.\n\n"
        "# Notes\n\n"
        "- Reflect the content to be appealing and joyous.\n"
        "- Ensure clear differentiation from other facilities in your descriptions.\n"
        "- Return ONLY the JSON dictionary without any additional text or formatting."
    )
    return prompt
```

**システムプロンプトの構成要素:**

1. **役割定義**: 「プロフェッショナルなホテルプランナー」として位置づけ
2. **タスク指示**: 季節テーマの反映、食事説明の詳細化等の具体的要求
3. **フォーマット要件**: 厳密なJSON形式での出力指定
4. **データ作成ガイドライン**: 各フィールドの文字数制限と内容要求
5. **注意事項**: 魅力的で差別化された内容作成の指示

**出力フィールドの詳細:**

- **`title`**: 最大50文字のキャッチーなプランタイトル
- **`description`**: 宿泊体験をイメージできる魅力的な説明文
- **`meal_description`**: 食事内容の詳細説明（朝食・夕食がある場合）
- **`hotel_concept`**: ホテルの強みと特徴を強調したコンセプト
- **`coution`**: 注意事項や条件
- **`benefits`**: ユーザーメリットの箇条書き

#### 4.3.3 ユーザープロンプト動的生成

```python
def create_user_prompt(parameta: dict) -> str:
    """ユーザー入力とホテルデータに基づいてOpenAI API用のユーザープロンプトを作成"""
    try:
        # hotel_idが提供されている場合はホテル情報を取得
        hotel_data = {}
        if 'hotel_id' in parameta:
            try:
                scraped_data = Scraping_Rakuten_data()
                hotel_data = scraped_data.get_info(parameta['hotel_id'])
                logger.info(f"hotel_id {parameta['hotel_id']} のホテルデータスクレイピング成功")
            except Exception as e:
                logger.warning(f"ホテルデータ取得失敗: {e}")

        # ベースプロンプトメッセージを構築
        base_conditions = [
            f"[期間]: {parameta.get('start_tarm', 'N/A')}~{parameta.get('end_tarm', 'N/A')}",
            f"客室タイプ: {parameta.get('room_type', 'N/A')}",
            f"宿泊日の種別: {parameta.get('date_type', 'N/A')}",
            f"朝食の有無: {parameta.get('meal', 'N/A')}",
            f"特典: {parameta.get('benefits', 'N/A')}",
            f"入力特典: {parameta.get('input_perks', 'N/A')}"
        ]

        prompt_message = "では、以下の条件を踏まえてプランを生成してください\n" + "\n".join(base_conditions)

        # ホテル情報が利用可能な場合は追加
        if hotel_data:
            hotel_info = [
                f"ホテル名: {hotel_data.get('hotel_name', 'N/A')}",
                f"住所: {hotel_data.get('address', 'N/A')}",
                f"交通アクセス: {hotel_data.get('access', 'N/A')}",
                f"館内設備: {hotel_data.get('hotel_facility', 'N/A')}",
                f"部屋設備・備品: {hotel_data.get('room_facility', 'N/A')}",
                f"チェックイン: {hotel_data.get('check_in', 'N/A')}",
                f"チェックアウト: {hotel_data.get('check_out', 'N/A')}",
                f"条件・注意事項: {hotel_data.get('coution', 'N/A')}"
            ]

            prompt_message += "\n\nホテル情報:\n" + "\n".join(hotel_info)

        logger.info(f"ユーザープロンプト作成完了、長さ: {len(prompt_message)}")
        return prompt_message

    except Exception as e:
        logger.error(f"ユーザープロンプト作成エラー: {e}")
        raise Exception(f"ユーザープロンプト作成失敗: {e}")
```

**ユーザープロンプト生成プロセス:**

1. **入力データ検証**: 必要なパラメータの存在確認
2. **ホテルデータ統合**: 楽天APIからスクレイピングした詳細情報を取得
3. **条件リスト構築**: ユーザー指定の宿泊条件を構造化
4. **プロンプト結合**: 基本条件とホテル情報を組み合わせた完全なプロンプト生成

**プロンプト構成例:**

```
では、以下の条件を踏まえてプランを生成してください
[期間]: 2024-03-15~2024-03-17
客室タイプ: ツイン
宿泊日の種別: 平日
朝食の有無: 朝食付き
特典: 飲み放題
入力特典: 温泉利用券

ホテル情報:
ホテル名: 〇〇温泉ホテル
住所: 東京都〇〇区〇〇
交通アクセス: JR〇〇駅から徒歩5分
館内設備: 大浴場、レストラン、会議室
部屋設備・備品: WiFi、エアコン、冷蔵庫
チェックイン: 15:00
チェックアウト: 11:00
条件・注意事項: 禁煙室のみ
```

#### 4.3.4 プロンプト最適化のポイント

**1. 明確な指示**
- 具体的な出力形式の指定
- 文字数制限の明記
- 必須フィールドの定義

**2. コンテキスト提供**
- ホテルの詳細情報
- ユーザーの宿泊条件
- 季節や期間の考慮

**3. 品質管理**
- 一貫した出力形式の確保
- エラーハンドリングの実装
- ログによる追跡可能性

**4. パフォーマンス最適化**
- 適切なトークン数の設定
- タイムアウト管理
- リトライロジックの実装

この設計により、ユーザーの要求に応じた高品質なホテルプランを自動生成し、一貫した形式で出力することが可能になります。

---

## 🔍 5. フロントエンド - タスクステータスポーリング

### 5.1 タスクステータスポーリングロジック

```javascript
const handleCreatePlan = async (values) => {
  try {
    setOpen(true);
    setCheckLoading(true);
    setCheckError(false);
    setCheckErrorMessage("");
    
    const res = await dispatch(createPlan(values));

    // createPlanが拒否された場合（エラーが発生）をチェック
    if (createPlan.rejected.match(res)) {
      const error = res.payload || res.error;
      let errorMessage = "プラン作成中にエラーが発生しました";
      
      // 特定のエラー条件をチェック
      if (error.statusCode === 429) {
        errorMessage = "API呼び出し制限に達しました。しばらくしてから再試行してください。";
      } else if (error.statusCode === 400) {
        errorMessage = "リクエストデータが無効です。入力内容を確認してください。";
      } else if (error.statusCode === 500) {
        errorMessage = "サーバーエラーが発生しました。再試行してください。";
      }
      
      setCheckLoading(false);
      setCheckError(true);
      setErrorMessage(errorMessage);
      return;
    }

    const taskId = res.payload?.data?.task_id;
    
    if (!taskId) {
      setCheckLoading(false);
      setCheckError(true);
      setErrorMessage("プランタスクの作成に失敗しました。再試行してください。");
      return;
    }

    // タイムアウト保護付きポーリング開始
    let pollCount = 0;
    const maxPollCount = 120; // 10分（5秒 × 120 = 600秒）
    
    intervalRef.current = setInterval(() => {
      pollCount++;
      
      if (pollCount >= maxPollCount) {
        // タイムアウト保護
        setCheckLoading(false);
        setCheckComplete(false);
        setCheckError(true);
        setErrorMessage("リクエストがタイムアウトしました。プラン作成に時間がかかりすぎています。再試行してください。");
        clearInterval(intervalRef.current);
        return;
      }
      
      checkTaskStatusCreatePlan(taskId);
    }, 5000); // 5秒ごとにポーリング
    
    // 初回チェック
    checkTaskStatusCreatePlan(taskId);
    
  } catch (error) {
    console.error("エラー:", error);
    setCheckLoading(false);
    setCheckError(true);
    setErrorMessage("予期しないエラーが発生しました。再試行してください。");
  }
};
```

### 5.2 タスクステータスチェック機能

```javascript
const checkTaskStatusCreatePlan = async (taskId) => {
  try {
    const response = await http_common.get(`/api/gpt_plans/get/${taskId}`);

    // HTTPインターセプターがresponse.dataを直接返す
    if (response.status === 200) {
      // タスク正常完了
      setCheckLoading(false);
      setCheckComplete(true);
      setCheckError(false);
      setErrorMessage("");
      clearInterval(intervalRef.current);
      
      // リダイレクト用に結果をlocalStorageに保存
      if (response.data) {
        const planData = JSON.stringify(response.data);
        localStorage.setItem("latestPlanResult", planData);
        console.log("プラン作成成功、localStorageに保存");
      } else {
        console.error("レスポンスにデータなし");
      }
    } else if (response.status === 202 || response.status === "pending") {
      // タスク処理中
      console.log("タスク処理中...");
    }
  } catch (error) {
    console.error("タスクステータスチェックエラー:", error);
    
    // 異なるタイプのエラーを処理
    if (error.response) {
      const statusCode = error.response.status;
      const errorMessage = error.response.data?.message || "エラーが発生しました";
      
      if (statusCode === 429) {
        setCheckLoading(false);
        setCheckComplete(false);
        setCheckError(true);
        setErrorMessage("API呼び出し制限に達しました。しばらくしてから再試行してください。");
        clearInterval(intervalRef.current);
      } else if (statusCode === 500) {
        setCheckLoading(false);
        setCheckComplete(false);
        setCheckError(true);
        setErrorMessage(errorMessage);
        clearInterval(intervalRef.current);
      }
      // ... 他のエラーコードを処理
    } else if (error.request) {
      // ネットワークエラー
      setCheckLoading(false);
      setCheckComplete(false);
      setCheckError(true);
      setErrorMessage("ネットワークエラーです。接続を確認してください。");
      clearInterval(intervalRef.current);
    }
  }
};
```

---

## 📊 6. バックエンド - タスクステータスチェック API

### 6.1 タスクステータスエンドポイント

**ファイル**: `levitt_lite_be/gpt_plans/views.py`

```python
class GetGPTPlanAsyncView(APIView):
    permission_classes = [IsTokenValid, IsAuthenticated]

    def get(self, request, task_id):
        try:
            if not task_id or task_id == 'undefined':
                return Response({
                    "message": "無効なtask_id",
                    "status": status.HTTP_400_BAD_REQUEST,
                }, status=status.HTTP_400_BAD_REQUEST)

            logger.info(f"タスクステータス確認: {task_id}")
            task_result = AsyncResult(task_id)

            if task_result.ready():
                try:
                    result = task_result.get()

                    # 結果がエラーかどうかチェック
                    if isinstance(result, str):
                        try:
                            result_dict = json.loads(result)
                            if isinstance(result_dict, dict) and result_dict.get('error'):
                                logger.error(f"タスク {task_id} 失敗: {result_dict.get('message')}")
                                return Response({
                                    "message": result_dict.get('message', 'タスク失敗'),
                                    "status": status.HTTP_500_INTERNAL_SERVER_ERROR,
                                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                        except json.JSONDecodeError:
                            pass  # 通常処理を続行

                    # タスク正常完了
                    user = request.user
                    user_profile = user.users
                    user_profile.used_time += 1
                    user_profile.save()

                    logger.info(f"タスク {task_id} 正常完了")
                    return Response({
                        "message": "タスクデータの取得成功",
                        "data": result,
                        "status": status.HTTP_200_OK,
                    }, status=status.HTTP_200_OK)

                except Exception as e:
                    logger.error(f"タスク結果取得エラー {task_id}: {e}")
                    return Response({
                        "message": "タスク結果取得エラー",
                        "status": status.HTTP_500_INTERNAL_SERVER_ERROR,
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            else:
                # タスク処理中
                task_state = task_result.state
                logger.debug(f"タスク {task_id} 処理中、状態: {task_state}")

                return Response({
                    "message": "処理中",
                    "data": {"state": task_state},
                    "status": "pending",
                }, status=status.HTTP_202_ACCEPTED)

        except Exception as e:
            logger.error(f"タスク {task_id} チェックエラー: {e}")
            return Response({
                "message": "内部サーバーエラーが発生しました",
                "status": status.HTTP_500_INTERNAL_SERVER_ERROR,
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
```

---

## 🎯 7. フロントエンド - 結果表示

### 7.1 結果ページへのリダイレクト

```javascript
const redirectCreateRecommendPlan = () => {
  const planData = localStorage.getItem("latestPlanResult");
  if (planData) {
    // ページリロードを避けるためReact Router のhistory.pushを使用
    history.push({
      pathname: "/plan/result/new",
      state: { planResult: planData }
    });
  }
};
```

### 7.2 結果ページ処理

**ファイル**: `levitt_lite_fe/src/pages/plan/result/[id]/index.jsx`

```javascript
export default function PlanResultDetail() {
  const dispatch = useDispatch();
  const { id } = useParams();
  const history = useHistory();
  const location = useLocation();
  
  // location stateからplanResultを取得
  const planResult = location.state?.planResult;
  const { plan } = useSelector((state) => state.recommendPlan);
  
  // planResultが利用可能な場合はそれを使用、そうでなければReduxのplanを使用
  const [result, setResult] = useState(planResult || plan);
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);

  useEffect(() => {
    const planResult = location.state?.planResult;
    
    if (!id && !planResult) {
      history.push("/plan/create");
      return;
    }

    if (planResult) {
      // localStorageからデータを解析
      const parsedPlan = typeof planResult === 'string' ? 
        JSON.parse(planResult) : planResult;
      setResult(parsedPlan);
      getSaleDate(parsedPlan?.term);
    } else {
      // idがある場合はAPIから取得
      async function getPlanResult() {
        const res = await dispatch(getRecommendPlan(id));
        setResult(res.payload[0]);
      }
      getPlanResult();
    }
  }, [id, location.state, dispatch, history]);

  // プラン編集・保存用のフォーム設定
  const formikConfig = {
    initialValues: {
      title: result?.title || "",
      description: result?.description || "",
      term: result?.term || "",
      room_type: result?.room_type || "",
      benefits: result?.benefits || "",
      meal_type: result?.meal_type || "",
      budget: result?.budget || "20000",
      ota_sell: result?.ota_sell || [],
    },
    onSubmit: async (values, { setSubmitting }) => {
      setSubmitting(true);
      
      // OTA売上データ処理
      if (values.ota_sell.length === 0) {
        values.ota_sell = [];
      }
      
      // 追加データ
      const submitData = {
        ...values,
        hotel_name: result?.hotel_name,
        sales_start_period: startDate ? moment(startDate).format("YYYY-MM-DD") : null,
        sales_end_period: endDate ? moment(endDate).format("YYYY-MM-DD") : null,
      };

      try {
        // プランをデータベースに保存
        await dispatch(createRecommendPlan({ body: submitData }));
        setIsSubmitted(true);
        toast.success("プランが正常に登録されました。");
      } catch (error) {
        toast.error("プランの登録に失敗しました。");
      }
      
      setSubmitting(false);
    },
  };

  return (
    <div className={styles["plan-result-detail"]}>
      {/* プラン詳細と編集フォームを表示 */}
      <Formik {...formikConfig}>
        {/* フォーム実装 */}
      </Formik>
    </div>
  );
}
```

---

## 🛡️ 8. エラーハンドリングとユーザーエクスペリエンス

### 8.1 API呼び出し制限処理

**バックエンド**:

```python
class ApiCallLimitMixin:
    def check_api_call_limit(self, request):
        user_profile = request.user.users
        if user_profile.used_time >= user_profile.max_used_time:
            return Response({
                "message": "API呼び出し制限を超過しました",
                "status": status.HTTP_429_TOO_MANY_REQUESTS
            }, status=status.HTTP_429_TOO_MANY_REQUESTS)
        return None
```

**フロントエンド**:

```javascript
// 日本語エラーメッセージによるエラーハンドリング
const errorMessages = {
  429: "API呼び出し制限に達しました。しばらくしてから再試行してください。",
  400: "リクエストデータが無効です。入力内容を確認してください。",
  401: "認証に失敗しました。再度ログインしてください。",
  500: "サーバーエラーが発生しました。再試行してください。",
  0: "ネットワークエラーです。接続を確認してください。"
};
```

### 8.2 ローディング状態とプログレス表示

```javascript
// ローディング状態
const [checkLoading, setCheckLoading] = useState(false);
const [checkComplete, setCheckComplete] = useState(false);
const [checkError, setCheckError] = useState(false);
const [errorMessage, setErrorMessage] = useState("");

// プログレスモーダル
<Modal open={open} onClose={handleClose}>
  <Box className={styles["modal-container"]}>
    {checkLoading && (
      <div className={styles["loading-container"]}>
        <CircularProgress />
        <Typography>プランを作成中...</Typography>
      </div>
    )}
    
    {checkComplete && (
      <div className={styles["success-container"]}>
        <CheckCircleIcon color="success" />
        <Typography>プランが正常に作成されました！</Typography>
        <Button onClick={redirectCreateRecommendPlan}>
          作成結果を見る
        </Button>
      </div>
    )}
    
    {checkError && (
      <div className={styles["error-container"]}>
        <ErrorIcon color="error" />
        <Typography color="error">{errorMessage}</Typography>
        <Button onClick={handleClose}>閉じる</Button>
      </div>
    )}
  </Box>
</Modal>
```

---

## 🔄 9. 完全フローの要約

### 9.1 シーケンス図

```
ユーザー → フロントエンド → Redux → バックエンド → Celery → OpenAI
                                                    ↓
                                                  楽天API
                                                    ↓
フロントエンド ← Redux ← バックエンド ← Celery ← 結果処理
```

### 9.2 データフロー

1. **ユーザー入力** → フォーム検証 → 送信
2. **フロントエンド Redux** → HTTPリクエスト → `/api/gpt_plans/create/asy/`
3. **バックエンド API** → 検証 → 制限チェック → Celeryタスク作成
4. **Celery ワーカー** → OpenAI API → ホテルスクレイピング → データ処理
5. **フロントエンド ポーリング** → タスクステータス確認 → `/api/gpt_plans/get/{task_id}`
6. **タスク完了** → 結果保存 → 結果ページリダイレクト
7. **結果ページ** → データ表示 → 編集許可 → DBに保存

### 9.3 主要機能

- **非同期処理**: AI処理用のCeleryタスクキュー
- **リアルタイムポーリング**: 5秒ごとのフロントエンドタスクステータスポーリング
- **エラーハンドリング**: 日本語エラーメッセージによる包括的エラー処理
- **リトライロジック**: OpenAI APIとCeleryタスクの指数バックオフ
- **データ永続化**: データ転送用のLocalStorage
- **使用状況追跡**: API呼び出し制限と使用カウンター
- **ホテル統合**: 楽天からのホテルデータスクレイピング
- **レスポンシブUI**: ローディング状態とプログレス表示
