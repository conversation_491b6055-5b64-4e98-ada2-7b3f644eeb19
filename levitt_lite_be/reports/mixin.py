from datetime import datetime
from django.utils import timezone


class DateFilterMixin:
    def get_date_range(self, start_date, end_date):
        today = timezone.now()

        if start_date and end_date:
            start = datetime.fromisoformat(start_date)
            end = datetime.fromisoformat(end_date)
        elif start_date:
            start = datetime.fromisoformat(start_date)
            end = today
        elif end_date:
            start = datetime.min.replace(tzinfo=timezone.utc)
            end = datetime.fromisoformat(end_date)
        else:
            start = today.replace(day=1, hour=0, minute=0,
                                  second=0, microsecond=0)
            end = today

        return start, end
