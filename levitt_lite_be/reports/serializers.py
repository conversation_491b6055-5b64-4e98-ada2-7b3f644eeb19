from rest_framework import serializers
from plans.models import Plan

class PlanSerializer(serializers.ModelSerializer):
    number_nights = serializers.SerializerMethodField()

    class Meta:
        model = Plan
        fields = [
            'name', 'total_fee', 'number_nights']

    def get_number_nights(self, obj):
        if obj.checkin_time and obj.checkout_time:
            number_nights = (obj.checkout_time - obj.checkin_time).days
            return number_nights
        return None
