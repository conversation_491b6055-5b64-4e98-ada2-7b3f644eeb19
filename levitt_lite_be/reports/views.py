from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from plans.models import Plan
from group_plans.models import GroupPlans
from users.views import IsTokenValid, IsAuthenticated
from group_plans.serializers import GroupPlanSerializer
from .constants import GROUP_PLAN_ROOMTYPE_RANKING, PLAN_OTA_RANKING_ORDER_NUM, \
    TIME_RANGES, NEW_DAYS_OF_WEEK, OVERVIEW_ORDER_NUM
from .mixin import DateFilterMixin
from .utils import format_report_data, get_ja_title, get_top_plans_number_nights_overview, \
    get_top_by_lead_time_overview, get_top_nights_stay_overview, get_top_room_overview, \
    get_price_ranges, get_top_number_rooms_each_days, get_top_reservations_room_each_days, \
    get_top_by_weekday_price_range, get_top_reservation_overview
import datetime


# プランの概要
class ReportOverviewView(APIView, DateFilterMixin):
    permission_classes = [IsTokenValid, IsAuthenticated]

    @swagger_auto_schema(manual_parameters=[
        openapi.Parameter('start', openapi.IN_QUERY, description="Start date in YYYY-MM-DD format", type=openapi.TYPE_STRING),
        openapi.Parameter('end', openapi.IN_QUERY, description="End date in YYYY-MM-DD format", type=openapi.TYPE_STRING),
    ])
    def get(self, request):
        start_date = request.query_params.get('start')
        end_date = request.query_params.get('end')
        start, end = self.get_date_range(start_date, end_date)
        plans = Plan.objects.filter(user=request.user, reservation_time__range=(start, end), reservation_time__isnull=False)

        # プランランキング
        top_plans_number_nights = get_top_plans_number_nights_overview(plans)
        # リードタイムランキング
        top_lead_time = get_top_by_lead_time_overview(plans)
        # ルームタイプ別ランキング
        top_room = get_top_room_overview(plans)[:OVERVIEW_ORDER_NUM]
        # 泊数/滞在別
        top_nights_stay = get_top_nights_stay_overview(plans)

        custom_data = {
            "top_plans_number_nights": top_plans_number_nights,
            "top_lead_time": top_lead_time,
            "top_room": top_room,
            "top_nights_stay": top_nights_stay,
        }

        return Response({
            "message": "report data fetched successfully",
            "data": custom_data,
            "status": status.HTTP_200_OK,
        }, status=status.HTTP_200_OK)


class ReportGroupPlanEachRoomTypeRanking(APIView, DateFilterMixin):
    permission_classes = [IsTokenValid, IsAuthenticated]

    @swagger_auto_schema(manual_parameters=[
        openapi.Parameter('start', openapi.IN_QUERY, description="Start date in YYYY-MM-DD format", type=openapi.TYPE_STRING),
        openapi.Parameter('end', openapi.IN_QUERY, description="End date in YYYY-MM-DD format", type=openapi.TYPE_STRING),
    ])
    def get(self, request):
        user = request.user
        start_date = request.query_params.get('start')
        end_date = request.query_params.get('end')
        start, end = self.get_date_range(start_date, end_date)
        plans = Plan.objects.filter(user=user, reservation_time__range=(start, end), reservation_time__isnull=False)

        plan_data_dict = {}
        for plan in plans:
            number_nights = (plan.checkout_time - plan.checkin_time).days if plan.checkin_time and plan.checkout_time else 0
            if plan.name not in plan_data_dict:
                plan_data_dict[plan.name] = {
                    "number_nights": 0,
                    "total_fee": 0,
                    "room_type_nights": {},
                }
            plan_data_dict[plan.name]["number_nights"] += number_nights
            plan_data_dict[plan.name]["total_fee"] += plan.total_fee

            if plan.room_type not in plan_data_dict[plan.name]["room_type_nights"]:
                plan_data_dict[plan.name]["room_type_nights"][plan.room_type] = 0
            plan_data_dict[plan.name]["room_type_nights"][plan.room_type] += number_nights

        custom_data = []
        for plan_name, data in plan_data_dict.items():
            report_data = []
            total_nights = data["number_nights"]
            total_fee = data["total_fee"]
            room_type_nights = data["room_type_nights"]

            top_data = {
                "title": "泊数",
                "color": "#172B4D",
                "value": total_nights
            }
            report_data.append(top_data)

            sorted_room_types = sorted(room_type_nights.items(), key=lambda x: x[1], reverse=True)
            for rank, (room_type, nights) in enumerate(sorted_room_types, start=1):
                room_type_data = {
                    "title": room_type,
                    "color": GROUP_PLAN_ROOMTYPE_RANKING.get(f"top{rank}", {}).get("color", "#FFFFFF"),
                    "value": nights
                }
                report_data.append(room_type_data)

            average_price = total_fee / total_nights if total_nights > 0 else 0

            custom_data.append({
                "group_plan_name": plan_name,
                "number_nights": total_nights,
                "number_reservations": len(plans.filter(name=plan_name)),
                "average_price": average_price,
                "total_fee": total_fee,
                "report_data": report_data,
            })

        top_10_plans = sorted(custom_data, key=lambda x: (x["number_nights"], x["total_fee"]), reverse=True)[:10]
        return Response({
            "message": "report data fetched successfully",
            "data": top_10_plans,
            "status": status.HTTP_200_OK,
        }, status=status.HTTP_200_OK)


class ReportLeadTimePrice(APIView, DateFilterMixin):
    permission_classes = [IsTokenValid, IsAuthenticated]

    @swagger_auto_schema(manual_parameters=[
        openapi.Parameter('price_ranges',  openapi.IN_QUERY, description="Enter price ranges separated by commas", type=openapi.TYPE_STRING),
        openapi.Parameter('start', openapi.IN_QUERY, description="Start date in YYYY-MM-DD format", type=openapi.TYPE_STRING),
        openapi.Parameter('end', openapi.IN_QUERY, description="End date in YYYY-MM-DD format", type=openapi.TYPE_STRING),
    ])
    def get(self, request):
        start_date = request.query_params.get('start')
        end_date = request.query_params.get('end')
        start, end = self.get_date_range(start_date, end_date)
        plans = Plan.objects.filter(user=request.user, reservation_time__range=(start, end), reservation_time__isnull=False)
        price_ranges = get_price_ranges(request.query_params.get('price_ranges', None))
        price_ranges_list = [label for _, label in price_ranges]

        result = []
        detailed_plans = []

        for plan in plans:
            lead_time = (plan.checkin_time - plan.reservation_time).days
            in_time_ranges = ''
            in_price_ranges = ''

            for time_range in TIME_RANGES:
                time_start, time_end = time_range[0]
                if time_start <= lead_time < (time_end if time_end is not None else float('inf')):
                    in_time_ranges = time_range[1]
                    break

            for price_range in price_ranges:
                price_start, price_end = price_range[0]
                if price_start <= plan.total_fee < (price_end if price_end is not None else float('inf')):
                    in_price_ranges = price_range[1]
                    break

            if in_time_ranges and in_price_ranges:
                result.append({
                    'lead_time': lead_time,
                    'total_fee': plan.total_fee,
                    'name': plan.name,
                    'reservation_site': plan.reservation_site,
                    'id': plan.id,
                    'in_time_range': in_time_ranges,
                    'in_price_range': in_price_ranges,
                })

                # if 10000 <= plan.total_fee < 20000 and 14 <= lead_time < 29:
                #     detailed_plans.append({
                #         'name': plan.name,
                #         'lead_time': lead_time,
                #         'total_fee': plan.total_fee,
                #         'reservation_site': plan.reservation_site,
                #         'reservation_time': plan.reservation_time,
                #         'checkin_time': plan.checkin_time,
                #     })

        time_range_order = {label: index for index, (_, label) in enumerate(TIME_RANGES)}
        price_range_order = [label for _, label in price_ranges]
        grouped_data = {time_label: {label: 0 for label in price_range_order} for _, time_label in TIME_RANGES}
        for item in result:
            time_label = item['in_time_range']
            price_label = item['in_price_range']
            if time_label and price_label:
                grouped_data.setdefault(time_label, {label: 0 for label in price_range_order})
                grouped_data[time_label].setdefault(price_label, 0)
                grouped_data[time_label][price_label] += 1

        custom_data = [{
            'time_label': time_label,
            'price_ranges': [totals[label] for label in price_range_order],
            'price_ranges_list': price_ranges_list
        } for time_label, totals in sorted(grouped_data.items(), key=lambda x: time_range_order[x[0]])]

        return Response({
            "message": "report data fetched successfully",
            "data": custom_data,
            "detailed_plans": detailed_plans,
            "status": status.HTTP_200_OK,
        }, status=status.HTTP_200_OK)


class ReportTopRoomType(APIView, DateFilterMixin):
    permission_classes = [IsTokenValid, IsAuthenticated]

    @swagger_auto_schema(manual_parameters=[
        openapi.Parameter('start', openapi.IN_QUERY, description="Start date in YYYY-MM-DD format", type=openapi.TYPE_STRING),
        openapi.Parameter('end', openapi.IN_QUERY, description="End date in YYYY-MM-DD format", type=openapi.TYPE_STRING),
    ])
    def get(self, request):
        start_date = request.query_params.get('start')
        end_date = request.query_params.get('end')
        start, end = self.get_date_range(start_date, end_date)
        plans = Plan.objects.filter(user=request.user, reservation_time__range=(start, end), reservation_time__isnull=False)

        result_data = get_top_room_overview(plans)
        return Response({
            "message": "report data fetched successfully",
            "data": result_data[:PLAN_OTA_RANKING_ORDER_NUM],
            "status": status.HTTP_200_OK,
        }, status=status.HTTP_200_OK)


class ReportPlanByDayOfWeek(APIView, DateFilterMixin):
    permission_classes = [IsTokenValid, IsAuthenticated]

    @swagger_auto_schema(manual_parameters=[
        openapi.Parameter('start', openapi.IN_QUERY, description="Start date in YYYY-MM-DD format", type=openapi.TYPE_STRING),
        openapi.Parameter('end', openapi.IN_QUERY, description="End date in YYYY-MM-DD format", type=openapi.TYPE_STRING),
    ])
    def get(self, request):
        user = request.user
        start_date = request.query_params.get('start')
        end_date = request.query_params.get('end')
        start, end = self.get_date_range(start_date, end_date)
        plans = Plan.objects.filter(user=user, reservation_time__range=(start, end), reservation_time__isnull=False)

        plan_data = {}
        for plan in plans:
            checkin_date = plan.checkin_time
            day_of_week = checkin_date.weekday()
            plan_name = plan.name

            if plan_name not in plan_data:
                plan_data[plan_name] = {
                    "total": {
                        "count": 0,
                        "color": "#172B4D",
                        "plan_name": plan_name
                    },
                    "days": [0] * 7
                }

            plan_data[plan_name]["total"]["count"] += 1
            plan_data[plan_name]["days"][day_of_week] += 1

        result_data = []
        for plan_name, data in plan_data.items():
            result_data.append({
                "total": data["total"],
                "days": dict(zip(NEW_DAYS_OF_WEEK, data["days"]))
            })

        top_plans = sorted(result_data, key=lambda x: x['total']['count'], reverse=True)[:10]
        return Response({
            "message": "report data fetched successfully",
            "data": top_plans,
            "status": status.HTTP_200_OK,
        }, status=status.HTTP_200_OK)


class ReportPlanOtaRanking(APIView, DateFilterMixin):
    permission_classes = [IsTokenValid, IsAuthenticated]

    @swagger_auto_schema(manual_parameters=[
        openapi.Parameter('start', openapi.IN_QUERY, description="Start date in YYYY-MM-DD format", type=openapi.TYPE_STRING),
        openapi.Parameter('end', openapi.IN_QUERY, description="End date in YYYY-MM-DD format", type=openapi.TYPE_STRING),
    ])
    def get(self, request):
        user = request.user
        start_date = request.query_params.get('start')
        end_date = request.query_params.get('end')
        start, end = self.get_date_range(start_date, end_date)

        plans = Plan.objects.filter(user=user, reservation_time__range=(start, end), reservation_time__isnull=False)

        plan_summary = {}
        for plan in plans:
            plan_name = plan.name
            number_nights = (plan.checkout_time - plan.checkin_time).days if plan.checkin_time and plan.checkout_time else 0
            total_fee = plan.total_fee

            if plan_name not in plan_summary:
                plan_summary[plan_name] = {
                    "number_reservations": 0,
                    "total_fee": 0,
                    "total_nights": 0
                }

            plan_summary[plan_name]["number_reservations"] += 1
            plan_summary[plan_name]["total_fee"] += total_fee
            plan_summary[plan_name]["total_nights"] += number_nights

        custom_data = []
        for name, data in plan_summary.items():
            average_price = data["total_fee"] / data["number_reservations"] if data["number_reservations"] > 0 else 0
            custom_data.append({
                "name": name,
                "number_reservations": data["number_reservations"],
                "average_price": average_price,
                "total_fee": data["total_fee"],
                "total": format_report_data("number_nights_lines", data["total_nights"]),
            })

        top_10_group_plans = sorted(
            custom_data,
            key=lambda x: (x["total"].get("number_nights", 0), x["total_fee"]),
            reverse=True,
        )[:PLAN_OTA_RANKING_ORDER_NUM]

        return Response({
            "message": "report data fetched successfully",
            "data": top_10_group_plans,
            "status": status.HTTP_200_OK,
        }, status=status.HTTP_200_OK)


class ReportDaysOfWeekOverview(APIView, DateFilterMixin):
    permission_classes = [IsTokenValid, IsAuthenticated]

    @swagger_auto_schema(manual_parameters=[
        openapi.Parameter('price_ranges',  openapi.IN_QUERY, description="Enter price ranges separated by commas", type=openapi.TYPE_STRING),
        openapi.Parameter('start', openapi.IN_QUERY, description="Start date in YYYY-MM-DD format", type=openapi.TYPE_STRING),
        openapi.Parameter('end', openapi.IN_QUERY, description="End date in YYYY-MM-DD format", type=openapi.TYPE_STRING),
    ])
    def get(self, request):
        start_date = request.query_params.get('start')
        end_date = request.query_params.get('end')
        start, end = self.get_date_range(start_date, end_date)
        plans = Plan.objects.filter(user=request.user, reservation_time__range=(start, end), reservation_time__isnull=False)
        price_ranges = get_price_ranges(request.query_params.get('price_ranges', None))
        price_ranges_list = [label for _, label in price_ranges]

        custom_data = {
            # 客室タイプ別予約件数(予約実績)
            'top_reservations_room_each_days': get_top_reservations_room_each_days(plans),
            # 宿泊単価別予約件数(予約実績)
            'top_number_rooms_each_days': get_top_number_rooms_each_days(plans),
            # 客室タイプ別宿泊件数(宿泊実績)
            'top_reservation_overview': get_top_reservation_overview(plans, price_ranges),
            # 宿泊単価別宿泊件数(宿泊実績)
            'top_by_weekday_price_range': get_top_by_weekday_price_range(plans, price_ranges),
            'price_ranges_list': price_ranges_list,
        }

        return Response({
            "message": "Report data fetched successfully",
            "data": custom_data,
            "status": status.HTTP_200_OK,
        }, status=status.HTTP_200_OK)
