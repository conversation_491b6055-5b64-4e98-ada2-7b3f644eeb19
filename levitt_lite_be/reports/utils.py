from django.db.models.functions import ExtractDay
from collections import defaultdict
from django.db.models import Count, Q, F, Sum
from plans.models import Plan
from .constants import OVERVIEW_ORDER_NUM, TIME_RANGES, PRICE_RANGES, \
    PLAN_OTA_RANKING_LINE, NUMBER_NIGHTS_LABELS, DAYS_OF_WEEK, \
    ONE_DAY_GROUP_COLORS, MAIN_COLOR, NEW_DAYS_OF_WEEK
import datetime

def transformed_days_data(days):
    return {
        "sun": days[0], "mon": days[1], "tue": days[2],
        "wed": days[3], "thu": days[4], "fri": days[5],
        "sat": days[6]
    }

def format_report_data(key, number_nights):
    return {
        "number_nights": number_nights,
        "color": PLAN_OTA_RANKING_LINE[key]['color'],
        "title": PLAN_OTA_RANKING_LINE[key]['ja_title']
    }

def get_ja_title(key):
    return PLAN_OTA_RANKING_LINE[key]['ja_title']

def get_price_ranges(range):
    if range and len(range.split(',')) == 4:
        range_list = [int(item) for item in range.split(',')]
        formatted_range_list = [f"{int(price):,}" for price in range_list]
        return [
            ((0, range_list[0]), f'~¥{formatted_range_list[0]}'),
            ((range_list[0], range_list[1]), f'~¥{formatted_range_list[1]}'),
            ((range_list[1], range_list[2]), f'~¥{formatted_range_list[2]}'),
            ((range_list[2], range_list[3]), f'~¥{formatted_range_list[3]}'),
            ((range_list[3], None), f'¥{formatted_range_list[3]}~'),
        ]
    return PRICE_RANGES

# OVERVIEW REPORT API
def get_top_nights_stay_overview(plans):
    plans = plans.annotate(number_nights=ExtractDay(F('checkout_time')) - ExtractDay(F('checkin_time')))
    results = []

    nights_summary = defaultdict(lambda: {'plan_count': 0, 'total_fee': 0})

    for plan in plans:
        number_nights = plan.number_nights
        if number_nights > 0:
            nights_summary[number_nights]['plan_count'] += 1
            nights_summary[number_nights]['total_fee'] += plan.total_fee

    for nights, data in nights_summary.items():
        results.append({
            'label_name': f'{nights}泊',
            'plan_count': data['plan_count'],
            'total_fee': data['total_fee']
        })

    results.sort(key=lambda x: x['plan_count'], reverse=True)
    return results[:OVERVIEW_ORDER_NUM]


def get_top_room_overview(plans):
    result_data = []
    room_type_totals = {}

    for plan in plans:
        room_type = plan.room_type if plan.room_type else "室タイプ不明"
        number_nights = plan.number_room
        total_fee = plan.total_fee

        key = (plan.name, plan.reservation_site)
        reservation_amount = room_type_totals.get(key, 0) + total_fee
        room_type_totals[key] = reservation_amount

        existing_room_type = next((item for item in result_data if item['room_type'] == room_type), None)
        if existing_room_type:
            existing_room_type['number_nights'] += number_nights
            existing_room_type['total_fee'] += total_fee
            existing_room_type['number_reservations'] += 1
        else:
            result_data.append({
                'room_type': room_type,
                'number_nights': number_nights,
                'total_fee': total_fee,
                'number_reservations': 1,
                'color': MAIN_COLOR,
                'title': room_type,
                'label_name': room_type,
            })

    for item in result_data:
        item['average_price'] = item['total_fee'] / item['number_nights'] if item['number_nights'] > 0 else 0

    result_data.sort(key=lambda x: x['number_nights'], reverse=True)
    return result_data


def get_top_by_lead_time_overview(plans):
    lead_time_summary = {case[1]: {'total_fee': 0, 'total_number_nights': 0} for case in TIME_RANGES}

    for plan in plans:
        lead_time = max((plan.checkin_time - plan.reservation_time).days, 0)

        for case in TIME_RANGES[:6]:
            if case[0][0] <= lead_time < (case[0][1] if case[0][1] is not None else float('inf')):
                lead_time_summary[case[1]]['total_fee'] += plan.total_fee
                lead_time_summary[case[1]]['total_number_nights'] += plan.number_room
                break

    summary = [{
        'label_name': label,
        'total_fee': data['total_fee'],
        'total_number_nights': data['total_number_nights'],
    } for label, data in lead_time_summary.items()]

    summary.sort(key=lambda x: x['total_fee'], reverse=True)
    return summary[:OVERVIEW_ORDER_NUM]


def get_top_plans_number_nights_overview(plans):
    result = {}

    for plan in plans:
        if plan.name not in result:
            result[plan.name] = {
                'total_fee': 0,
                'number_nights': 0
            }

        result[plan.name]['total_fee'] += plan.total_fee
        result[plan.name]['number_nights'] += plan.number_room

    final_result = [{
        'label_name': name,
        'total_fee': data['total_fee'],
        'number_nights': data['number_nights']
    } for name, data in result.items() if data['number_nights'] > 0 or data['total_fee'] > 0]

    final_result = sorted(final_result, key=lambda x: (x['number_nights'], x['total_fee']), reverse=True)
    return final_result[:OVERVIEW_ORDER_NUM]


# OVERVIEW REPORT DAYS OF WEEK API
def get_top_reservations_room_each_days(plans):
    room_type_counts = defaultdict(lambda: defaultdict(int))

    for plan in plans:
        day_of_week = plan.reservation_time.weekday()
        room_type = plan.room_type if plan.room_type is not None else '室タイプ不明'
        room_type_counts[NEW_DAYS_OF_WEEK[day_of_week]][room_type] += 1

    custom_data = defaultdict(list)
    for day, room_types in room_type_counts.items():
        sorted_room_types = sorted(room_types.items(), key=lambda x: x[1], reverse=True)[:5]
        for i, (room_type, count) in enumerate(sorted_room_types):
            color_index = i % len(ONE_DAY_GROUP_COLORS)
            color = ONE_DAY_GROUP_COLORS[color_index]
            custom_data[day].append({
                "room_type": room_type,
                "total_reservations": count,
                "color": color
            })

    return dict(custom_data)


def get_top_number_rooms_each_days(plans):
    room_type_counts = defaultdict(lambda: defaultdict(int))

    for plan in plans:
        checkin_date = plan.checkin_time
        room_type = plan.room_type if plan.room_type is not None else '室タイプ不明'
        day_of_week = checkin_date.weekday()
        room_type_counts[NEW_DAYS_OF_WEEK[day_of_week]][room_type] += 1

    custom_data = defaultdict(list)
    for day, room_types in room_type_counts.items():
        sorted_room_types = sorted(room_types.items(), key=lambda x: x[1], reverse=True)[:5]
        for i, (room_type, count) in enumerate(sorted_room_types):
            color_index = i % len(ONE_DAY_GROUP_COLORS)
            color = ONE_DAY_GROUP_COLORS[color_index]
            custom_data[day].append({
                "room_type": room_type,
                "total_rooms": count,
                "color": color
            })

    return dict(custom_data)


def get_top_reservation_overview(plans, price_ranges):
    count_by_day_and_range = defaultdict(lambda: defaultdict(int))

    for plan in plans:
        day_of_week = NEW_DAYS_OF_WEEK[plan.reservation_time.weekday()]
        total_fee = plan.total_fee
        price_range_label = None

        for (start, end), label in price_ranges:
            if end is None and total_fee >= start:
                price_range_label = label
                break
            elif start < total_fee <= end:
                price_range_label = label
                break

        if price_range_label:
            count_by_day_and_range[day_of_week][price_range_label] += 1

    formatted_data = defaultdict(list)
    for day in NEW_DAYS_OF_WEEK:
        for i, ((_, _), label) in enumerate(price_ranges):
            color_index = i % len(ONE_DAY_GROUP_COLORS)
            color = ONE_DAY_GROUP_COLORS[color_index]
            count = count_by_day_and_range[day][label]
            formatted_data[day].append({
                "price_range": label,
                "number_reservations": count,
                "color": color
            })

    return dict(formatted_data)

def get_top_by_weekday_price_range(plans, price_ranges):
    count_by_day_and_range = defaultdict(lambda: defaultdict(int))

    for plan in plans:
        checkin_date = plan.checkin_time
        total_fee = plan.total_fee
        day_of_week = NEW_DAYS_OF_WEEK[checkin_date.weekday()]
        price_range_label = None

        for (start, end), label in price_ranges:
            if end is None and total_fee >= start:
                price_range_label = label
                break
            elif start <= total_fee < end:
                price_range_label = label
                break

        if price_range_label:
            count_by_day_and_range[day_of_week][price_range_label] += 1

    formatted_data = defaultdict(list)
    for day in NEW_DAYS_OF_WEEK:
        for i, ((_, _), label) in enumerate(price_ranges):
            color = ONE_DAY_GROUP_COLORS[i % len(ONE_DAY_GROUP_COLORS)]
            number_nights = count_by_day_and_range[day][label]
            formatted_data[day].append({
                "range": label,
                "number_nights": number_nights,
                "color": color
            })

    return dict(formatted_data)
