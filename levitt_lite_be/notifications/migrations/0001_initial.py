# Generated by Django 4.2.5 on 2023-10-04 16:00

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ('status', models.Char<PERSON>ield(max_length=50)),
                ('category', models.Char<PERSON>ield(max_length=50)),
                ('visibility', models.CharField(max_length=50)),
                ('publication_date', models.DateTimeField()),
                ('content', models.TextField()),
                ('recipients', models.ManyTo<PERSON>anyField(related_name='notifications', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
