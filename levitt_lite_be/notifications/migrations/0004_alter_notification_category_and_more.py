# Generated by Django 4.2.4 on 2023-10-17 04:12

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("notifications", "0003_remove_notification_recipients_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="notification",
            name="category",
            field=models.IntegerField(
                blank=True,
                choices=[(1, "お知らせ"), (2, "アップデート")],
                null=True,
                verbose_name="カテゴリー",
            ),
        ),
        migrations.AlterField(
            model_name="notification",
            name="content",
            field=models.TextField(blank=True, null=True, verbose_name="本文"),
        ),
        migrations.AlterField(
            model_name="notification",
            name="publication_date",
            field=models.DateTimeField(blank=True, null=True, verbose_name="公開日"),
        ),
        migrations.AlterField(
            model_name="notification",
            name="recipients",
            field=models.IntegerField(
                blank=True,
                choices=[(1, "LP"), (2, "管理画面"), (3, "すべて")],
                null=True,
                verbose_name="公開先",
            ),
        ),
        migrations.AlterField(
            model_name="notification",
            name="status",
            field=models.IntegerField(
                blank=True,
                choices=[(1, "公開"), (2, "非公開")],
                null=True,
                verbose_name="ステータス",
            ),
        ),
        migrations.AlterField(
            model_name="notification",
            name="title",
            field=models.CharField(
                blank=True, max_length=255, null=True, verbose_name="タイトル"
            ),
        ),
        migrations.AlterField(
            model_name="notification",
            name="visibility",
            field=models.IntegerField(
                blank=True,
                choices=[(1, "全員"), (2, "無料アカウント"), (3, "有料アカウント")],
                null=True,
                verbose_name="公開範囲",
            ),
        ),
    ]
