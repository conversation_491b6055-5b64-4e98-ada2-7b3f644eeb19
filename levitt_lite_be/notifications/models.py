from django.db import models

STATUS_OPTION = [
    (1, "公開"),
    (2, "非公開"),
]

CATEGORY_OPTION = [
    (1, "お知らせ"),
    (2, "アップデート"),
]

RECIPIENT_OPTION = [
    (1, "LP"),
    (2, "管理画面"),
    (3, "すべて"),
]

VISIBILITY_OPTION = [
    (1, "全員"),
    (2, "無料アカウント"),
    (3, "有料アカウント"),
]

NOT_PUBLIC = 1

class Notification(models.Model):
    title = models.CharField(max_length=255, null=True, blank=True, verbose_name="タイトル")
    status = models.IntegerField(choices=STATUS_OPTION, null=True, blank=True, verbose_name="ステータス")
    category = models.IntegerField(choices=CATEGORY_OPTION, null=True, blank=True, verbose_name="カテゴリー")
    recipients = models.IntegerField(choices=RECIPIENT_OPTION, null=True, blank=True, verbose_name="公開先")
    visibility = models.IntegerField(choices=VISIBILITY_OPTION, null=True, blank=True, verbose_name="公開範囲")
    publication_date = models.DateTimeField(null=True, blank=True, verbose_name="公開日")
    content = models.TextField(null=True, blank=True, verbose_name="本文")

    class Meta:
        db_table = 'notifications'

    def __str__(self):
        return self.title
