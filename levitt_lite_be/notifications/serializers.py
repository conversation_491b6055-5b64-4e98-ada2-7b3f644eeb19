# notifications/serializers.py
from rest_framework import serializers
from .models import Notification, STATUS_OPTION, CATEGORY_OPTION, RECIPIENT_OPTION, VISIBILITY_OPTION


class NotificationSerializer(serializers.ModelSerializer):
    title = serializers.CharField(max_length=255, required=True)
    status = serializers.ChoiceField(choices=STATUS_OPTION, required=True)
    category = serializers.ChoiceField(choices=CATEGORY_OPTION, required=True)
    recipients = serializers.ChoiceField(choices=RECIPIENT_OPTION, required=True)
    visibility = serializers.ChoiceField(choices=VISIBILITY_OPTION, required=True)
    publication_date = serializers.DateTimeField(required=True)
    content = serializers.CharField(required=True)

    class Meta:
        model = Notification
        fields = '__all__'
