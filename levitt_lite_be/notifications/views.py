from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import viewsets
from rest_framework.response import Response
from rest_framework.views import APIView
from starlette import status
from .models import Notification, STATUS_OPTION, CATEGORY_OPTION, RECIPIENT_OPTION, VISIBILITY_OPTION , NOT_PUBLIC
from .serializers import NotificationSerializer
from rest_framework.pagination import PageNumberPagination
from users.views import IsAdminUser, IsTokenValid, IsAuthenticated
from django.db.models import Q
from datetime import datetime


class CustomPaginationClassNotifications(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'


class NotificationViewSet(viewsets.ModelViewSet):
    queryset = Notification.objects.all()
    serializer_class = NotificationSerializer

    def get_permissions(self):
        if self.action in ["create", "update", "destroy"]:
            permission_classes = [IsAdminUser, IsTokenValid, IsAuthenticated]
        else:
            permission_classes = []
        return [permission() for permission in permission_classes]

    def search_param(self):
        queryset = Notification.objects.all().order_by('-publication_date')
        category = self.request.query_params.get('category', None)
        if category is not None:
            queryset = queryset.filter(category=category)
            today = datetime.today().date()
            queryset = queryset.filter(publication_date__lte=today)

        recipients = self.request.query_params.get('recipients', None)
        if recipients is not None:
            queryset = queryset.filter(Q(recipients=recipients) | Q(recipients=3))

        status = self.request.query_params.get('status', None)
        if status is not None:
            queryset = queryset.filter(status=status)


        limit = self.request.query_params.get('limit', None)
        if limit is not None:
            limit = int(limit)
            today = datetime.today().date()
            queryset = queryset.filter(publication_date__lte=today)[:limit]

        return queryset

    @swagger_auto_schema(manual_parameters=[
        openapi.Parameter('is_admin_interface', openapi.IN_QUERY, description="Your parameter description",
                          type=openapi.TYPE_STRING),
        openapi.Parameter('category', openapi.IN_QUERY, description="Your parameter description",
                          type=openapi.TYPE_STRING),
        openapi.Parameter('limit', openapi.IN_QUERY, description="Your parameter description",
                          type=openapi.TYPE_STRING),
    ])
    def list(self, request, *args, **kwargs):
        queryset = self.search_param()
        isAdminInterface = self.request.query_params.get('is_admin_interface', False)
        if isAdminInterface:
            paginator = CustomPaginationClassNotifications()
        else:
            paginator = PageNumberPagination()
        page = paginator.paginate_queryset(queryset, request)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            custom_data = {
                "message": "Notifications fetched successfully",
                "data": serializer.data,
                "total_pages": paginator.page.paginator.num_pages,
                "status": status.HTTP_200_OK
            }
            return paginator.get_paginated_response(custom_data)

        # Serialize the queryset
        serializer = self.get_serializer(queryset, many=True)

        # Customize the response data as needed
        custom_data = {
            "message": "Notifications fetched successfully",
            "data": serializer.data,
            "status": status.HTTP_200_OK
        }

        return Response(custom_data)

    def retrieve(self, request, *args, **kwargs):
        is_superuser = request.user.is_superuser
        instance = self.get_object()

        if not is_superuser:
            if instance.status != NOT_PUBLIC:
                return Response({"message": "アクセスできない"}, status=status.HTTP_403_FORBIDDEN)

        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)

        if serializer.is_valid():
            serializer.save()

            return Response({
                "message": "お知らせが正常に作成されました。",
                "status": status.HTTP_201_CREATED
            }, status=status.HTTP_201_CREATED)
        else:
            return Response({
                "message": "作成に失敗しました。",
                "status": status.HTTP_400_BAD_REQUEST
            }, status=status.HTTP_400_BAD_REQUEST)

    def update(self, request, pk=None):
        notification = self.get_object()
        serializer = self.get_serializer(
            notification, data=request.data, partial=True)

        if serializer.is_valid():
            serializer.save()
            return Response({
                "message": "お知らせが正常に編集されました。",
                "status": status.HTTP_200_OK
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                "message": "編集に失敗しました。",
                "status": status.HTTP_400_BAD_REQUEST
            }, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, pk=None):
        try:
            notification = self.get_object()
            notification.delete()
            return Response({
                "message": "お知らせが正常に削除されました。",
                "status": status.HTTP_204_NO_CONTENT
            }, status=status.HTTP_204_NO_CONTENT)
        except Exception as e:
            return Response({
                "message": "削除に失敗しました。",
                "status": status.HTTP_400_BAD_REQUEST
            }, status=status.HTTP_400_BAD_REQUEST)


class NotificationSelectOptionsView(APIView):
    def get(self, request, *args, **kwargs):
        status_options = STATUS_OPTION
        category_options = CATEGORY_OPTION
        recipient_options = RECIPIENT_OPTION
        visibility_options = VISIBILITY_OPTION

        data = {
            "status_options": status_options,
            "category_options": category_options,
            "recipient_options": recipient_options,
            "visibility_options": visibility_options,
        }

        return Response({
            "data": data,
            "status": status.HTTP_200_OK
        }, status=status.HTTP_200_OK)
