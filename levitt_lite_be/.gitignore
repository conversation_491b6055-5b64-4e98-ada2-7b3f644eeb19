# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Local development settings
.env
.env.local
.env.*.local
venv/
*.sqlite3
uwsgi.ini
*.pid

# Log files
*.log

# Media files
media/

# Static files
static/

# Compiled Python modules
*.pyc

# Django migration files
**/migrations/*.bak
**/migrations/*.swp
**/migrations/*.pyc
**/migrations/__pycache__/

# Ignore VS Code settings
.vscode/

# Ignore macOS system files
.DS_Store

# Ignore Python virtual environment files
env/

.idea

# Ignore avatar image files
avatars/

# Ignore DB cache file
dump.rdb