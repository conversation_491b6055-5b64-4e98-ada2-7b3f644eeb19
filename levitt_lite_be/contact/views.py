import os
import requests
import logging
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from users.views import IsTokenValid
from users.models import Users

logger = logging.getLogger(__name__)


class ContactView(APIView):
    permission_classes = [IsTokenValid, IsAuthenticated]

    def post(self, request, *args, **kwargs):
        user = request.user
        user_profile = Users.objects.get(user=user)
        hotel = user_profile.hotel
        contact_data = request.data
        message = (
            f"氏名：{user.last_name}{user.first_name}\n"
            f"メールアドレス：{user.email}\n"
            f"施設名：{hotel.name if hotel else '未設定'}\n"
            f"電話番号：{contact_data['phone']}\n\n"
            f"お問い合わせ内容：\n{contact_data['contact']}"
        )

        # Slackに通知
        slack_webhook_url = os.environ.get('SLACK_WEBHOOK_URL')
        slack_message = {'text': message}
        response = requests.post(slack_webhook_url, json=slack_message)
        if response.status_code != 200:
            # Slackへの通知送信にエラーが発生した場合の処理
            error_message = f"Slackへの通知送信に失敗しました。ステータスコード: {response.status_code}"
            logger.error(error_message)
            # エラー通知等の処理をここに追加 (必要に応じて)
            return Response({"message": "お問い合わせに失敗しました。"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response({"message": "お問い合わせありがとうございます。"}, status=status.HTTP_200_OK)
