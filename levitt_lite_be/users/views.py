import random
import string
import urllib.parse
import os
import requests
from django.contrib.auth.hashers import make_password
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth.models import User
from django.contrib.auth.password_validation import validate_password
from django.contrib.auth.tokens import default_token_generator
from django.utils.http import urlsafe_base64_encode
from django.utils.encoding import force_bytes
from django.core.mail import send_mail
from .email_utils import send_activation_email, send_password_reset_email, send_admin_notification_email
from django.conf import settings
from django.utils.http import urlsafe_base64_decode
from django.core.exceptions import ValidationError
from rest_framework import status, pagination
from rest_framework.permissions import IsAuthenticated, BasePermission
from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from users.models import Users, BlackListedToken
from .serializers import CustomTokenObtainPairSerializer, UpdateProfileSerializer, UpdatePartnerIdSerializer
from rest_framework_simplejwt.views import TokenObtainPairView
from django.utils.encoding import force_str
from users.serializers import (
    UserSerializer, UserDetailSerializer, CreateUserSerializer, ActivateUserSerializer,
    UpdateUserSerializer, CreateAccountSerializer, PasswordResetRequestSerializer, PasswordResetSerializer
)
from hotels.models import Hotels
from .models import ACCOUNT_OPTION, RATE_OPTION
from django.utils import timezone

POINT_PER_INVITE = 5
INVITE_CODE_LENGTH = 16

class CreateUserView(APIView):
    def post(self, request, format=None):
        serializer = CreateAccountSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            is_superuser = False
            is_active = serializer.validated_data.get('is_active', False)
            account_category = serializer.validated_data['account_category']
            rate = serializer.validated_data['rate']
            user = User.objects.create_user(
                username=email,
                email=email,
                is_active=is_active,
                is_superuser=is_superuser)
            users = Users(user=user,
                          account_category=account_category,
                          rate=rate)
            users.save()

            token = default_token_generator.make_token(user)
            uid = urlsafe_base64_encode(force_bytes(user.pk))
            url_frontend = os.getenv('URL_FRONTEND')
            activation_url = (f'{url_frontend}/register/{uid}/{token}?email={urllib.parse.quote(email, safe="")}')

            # Use improved email utility
            email_sent = send_activation_email(email, activation_url)

            if not email_sent:
                return Response({
                    'message': 'アクティベーションメールの送信に失敗しました。',
                    'status': status.HTTP_500_INTERNAL_SERVER_ERROR
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            return Response({
                'message': '入力したメールアドレスにアクティベーションメールを送信しました。メールを確認してアクティベーションしてください。',
                'status': status.HTTP_201_CREATED,
            }, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class RegisterUserView(APIView):
    def post(self, request, format=None):
        serializer = CreateUserSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            is_superuser = serializer.validated_data.get('is_superuser', False)

            user = User.objects.create_user(
                username=email, email=email, is_active=False, is_superuser=is_superuser)

            token = default_token_generator.make_token(user)
            uid = urlsafe_base64_encode(force_bytes(user.pk))
            url_frontend = os.getenv('URL_FRONTEND')
            activation_url = f'{url_frontend}/register/{uid}/{token}?email={urllib.parse.quote(email, safe="")}'

            # Use improved email utility
            email_sent = send_activation_email(email, activation_url)

            if not email_sent:
                return Response({
                    'message': 'アクティベーションメールの送信に失敗しました。',
                    'status': status.HTTP_400_BAD_REQUEST
                }, status=status.HTTP_400_BAD_REQUEST)

            return Response({
                'message': '入力したメールアドレスにアクティベーションメールを送信しました。メールを確認してアクティベーションしてください。',
                'status': status.HTTP_201_CREATED,
            }, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ActivateUserView(APIView):
    def post(self, request, format=None):
        serializer = ActivateUserSerializer(data=request.data)
        if serializer.is_valid():
            uid = serializer.validated_data['uid']
            token = serializer.validated_data['token']
            first_name = serializer.validated_data.get('first_name')
            last_name = serializer.validated_data.get('last_name')
            password = serializer.validated_data.get('password')
            hotel_id = serializer.validated_data.get('hotel_id')
            invite_code = request.data.get('invitation_code')

            # Add 5 points to the user who invited
            if invite_code:
               try:
                   userInvite = Users.objects.get(invite_code=invite_code)
                   if userInvite is not None:
                       userInvite.rate = userInvite.rate + POINT_PER_INVITE
                       userInvite.save()
               except Users.DoesNotExist:
                   print("User Invite does not exist")

            # Handle active user
            try:
                user_id_bytes = urlsafe_base64_decode(uid)
                user_id_str = user_id_bytes.decode('utf-8')
                user = User.objects.get(pk=user_id_str)

                if default_token_generator.check_token(user, token):
                    user.is_active = True
                    if first_name:
                        user.first_name = first_name
                    if last_name:
                        user.last_name = last_name

                    if password:
                        try:
                            # Validate the password
                            validate_password(password, user=user)
                            # Hash and set the password
                            user.password = make_password(password)
                        except ValidationError as e:
                            return Response({
                                'error': e.messages,
                                'status': status.HTTP_400_BAD_REQUEST
                            }, status=status.HTTP_400_BAD_REQUEST)

                    user.save()

                    user_invite_code =  ''.join(random.choices(string.ascii_uppercase +  string.digits, k=INVITE_CODE_LENGTH)) + str(user.id)
                    users = Users(user=user, hotel_id=hotel_id, invite_code=user_invite_code, rate=POINT_PER_INVITE)
                    # users = Users(user=user, accommodation_name=accommodation_name, hotel_id=hotel_id)
                    users.save()

                    if user.is_active:
                        # Salesforceへの認証
                        auth_url = settings.SALESFORCE_LOGIN_URL + '/services/oauth2/token'
                        data = {
                            'grant_type': 'password',
                            'client_id': settings.SALESFORCE_CLIENT_ID,
                            'client_secret': settings.SALESFORCE_CLIENT_SECRET,
                            'username': settings.SALESFORCE_USERNAME,
                            'password': settings.SALESFORCE_PASSWORD
                        }
                        headers = {
                            'Content-Type': 'application/x-www-form-urlencoded'
                        }
                        auth_response = requests.post(auth_url, data=data, headers=headers)
                        if auth_response.status_code == 200:
                            access_token = auth_response.json().get('access_token')

                            # Salesforce APIを使用してデータを送信
                            api_url = settings.SALESFORCE_API_URL
                            hotel_name = users.hotel.name
                            salesforce_data = {
                                'LastName': last_name,  # 姓
                                'FirstName': first_name,  # 名
                                'Email': user.email,  # メールアドレス
                                'Company': hotel_name,  # 施設名
                                'Salutation': '様', # 敬称
                                'Status': '未対応', # リード状況
                                'score__c': '1', # スコア
                                'route__c': 'Levitt', # 流入経路
                            }
                            headers = {
                                'Authorization': f'Bearer {access_token}',
                                'Content-Type': 'application/json'
                            }
                            response = requests.post(api_url, json=salesforce_data, headers=headers)

                            if response.status_code != 201:
                                # SalesforceへのPOSTに失敗した場合のエラーハンドリング
                                print('Failed to post data to Salesforce', response.text)
                                # 必要に応じてエラー処理を追加
                        else:
                            # Salesforceへの認証に失敗した場合のエラーハンドリング
                            print('Failed to authenticate with Salesforce', auth_response.text)
                            # 必要に応じてエラー処理を追加

                        # Send admin notification email
                        send_admin_notification_email(user.email, first_name, last_name)
                    
                        return Response({
                            'message': 'User activated successfully',
                            'status': status.HTTP_201_CREATED,
                        }, status=status.HTTP_201_CREATED)
                else:
                    return Response('Activation failed',
                                    status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                return Response('Activation failed', status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserLoginView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer


class IsTokenValid(BasePermission):
    def has_permission(self, request, view):
        user_id = request.user.id
        is_allowed_user = True
        auth_header = request.META.get('HTTP_AUTHORIZATION')

        # Check if the header exists and starts with 'Bearer '
        if auth_header and auth_header.startswith('Bearer '):
            # Extract the access token by removing 'Bearer ' prefix
            access_token = auth_header.split(' ')[1]
            try:
                is_blacklisted = BlackListedToken.objects.get(
                    user=user_id, token=access_token)
                if is_blacklisted:
                    is_allowed_user = False
            except BlackListedToken.DoesNotExist:
                is_allowed_user = True

        return is_allowed_user


class UserProfileView(APIView):
    permission_classes = [IsTokenValid, IsAuthenticated]
    serializer_class = UserSerializer

    def get(self, request):
        user = request.user
        serializer = UserSerializer(user)
        return Response(serializer.data, status=status.HTTP_200_OK)


class LogoutView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        user = User.objects.get(id=request.user.id)
        refresh_token = request.data.get('refresh_token')
        auth_header = request.META.get('HTTP_AUTHORIZATION')
        if refresh_token and auth_header and auth_header.startswith('Bearer '):
            try:
                access_token = auth_header.split(' ')[1]
                BlackListedToken.objects.create(user=user, token=access_token)
                token = RefreshToken(refresh_token)
                token.blacklist()
                return Response({'message': 'Logged out successfully'}, status=status.HTTP_200_OK)
            except Exception as e:
                return Response({'message': 'Error revoking token'}, status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response({'message': 'Refresh token is required'}, status=status.HTTP_400_BAD_REQUEST)


class IsAdminUser(BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.is_superuser


class UpdateUserView(APIView):
    permission_classes = [IsTokenValid, IsAuthenticated, IsAdminUser]

    def put(self, request, pk):
        serializer = UpdateUserSerializer(data=request.data)

        if serializer.is_valid():
            user = get_object_or_404(User, pk=pk)
            first_name = serializer.validated_data.get('first_name')
            last_name = serializer.validated_data.get('last_name')
            user.first_name = first_name
            user.last_name = last_name
            user.save()

            rate = serializer.validated_data.get('rate')
            account_category = serializer.validated_data.get('account_category')
            hotel_id = serializer.validated_data.get('hotel_id')
            all_is_enabled = serializer.validated_data.get('all_is_enabled')
            plan_is_enabled = serializer.validated_data.get('plan_is_enabled')
            ad_is_enabled = serializer.validated_data.get('ad_is_enabled')
            coupon_is_enabled = serializer.validated_data.get('coupon_is_enabled')
            report_is_enabled = serializer.validated_data.get('report_is_enabled')
            is_accommodation_facility = serializer.validated_data.get('is_accommodation_facility')
            sale_is_enabled = serializer.validated_data.get('sale_is_enabled')

            users, is_created = Users.objects.get_or_create(user=user)

            if not is_created:
                if rate is not None:
                    users.rate = rate
                if account_category is not None:
                    users.account_category = account_category
                if hotel_id is not None:
                    try:
                        hotel = Hotels.objects.get(pk=hotel_id)
                        users.hotel = hotel
                    except Hotels.DoesNotExist:
                        pass
                if all_is_enabled is not None:
                    users.all_is_enabled = all_is_enabled
                if plan_is_enabled is not None:
                    users.plan_is_enabled = plan_is_enabled
                if ad_is_enabled is not None:
                    users.ad_is_enabled = ad_is_enabled
                if sale_is_enabled is not None:
                    users.sale_is_enabled = sale_is_enabled
                if coupon_is_enabled is not None:
                    users.coupon_is_enabled = coupon_is_enabled
                if report_is_enabled is not None:
                    users.report_is_enabled = report_is_enabled
                if is_accommodation_facility is not None:
                    users.is_accommodation_facility = is_accommodation_facility
                users.save()

            return Response({
                "message": "ユーザーが正常に編集されました。",
                "status": status.HTTP_200_OK
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                "message": serializer.errors,
                "status": status.HTTP_400_BAD_REQUEST
            }, status=status.HTTP_400_BAD_REQUEST)


class DeleteUserView(APIView):
    permission_classes = [IsTokenValid, IsAuthenticated, IsAdminUser]

    def delete(self, request, pk):
        try:
            user = User.objects.get(pk=pk)
            if user == request.user:
                return Response({
                    "message": "自分アカウントが削除できません。",
                    "status": status.HTTP_400_BAD_REQUEST
                }, status=status.HTTP_400_BAD_REQUEST)

            try:
                users_obj = user.users
                users_obj.delete()
            except Users.DoesNotExist:
                pass

            user.delete()
            return Response({
                "message": "ユーザーが正常に削除されました。",
                'status': status.HTTP_200_OK
            }, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({
                "message": "削除に失敗しました。",
                "status": status.HTTP_400_BAD_REQUEST
            }, status=status.HTTP_400_BAD_REQUEST)


class DeleteProfileView(APIView):
    permission_classes = [IsTokenValid, IsAuthenticated]

    def post(self, request, *args, **kwargs):
        try:
            user = request.user

            try:
                users_obj = user.users
                users_obj.delete()
            except Users.DoesNotExist:
                pass

            user.delete()
            return Response({
                "message": "ユーザーが正常に削除されました。",
                'status': status.HTTP_200_OK
            }, status=status.HTTP_200_OK)
        except Exception as e:
            print(str(e))
            return Response({
                "message": "削除に失敗しました。",
                "status": status.HTTP_400_BAD_REQUEST
            }, status=status.HTTP_400_BAD_REQUEST)


class ListUsersView(APIView):
    permission_classes = [IsTokenValid, IsAuthenticated, IsAdminUser]

    @swagger_auto_schema(manual_parameters=[openapi.Parameter('page', openapi.IN_QUERY, description="", type=openapi.TYPE_STRING)])
    def get(self, request):
        paginator = pagination.PageNumberPagination()

        users = User.objects.all()
        paginated_users = paginator.paginate_queryset(users, request)

        users_serializer = UserSerializer(paginated_users, many=True)
        custom_data = {
            "message": "ユーザーは正常に取得されました",
            "data": users_serializer.data,
            "status": status.HTTP_200_OK,
            "total_pages": paginator.page.paginator.num_pages
        }
        return paginator.get_paginated_response(custom_data)


class UserDetailView(APIView):
    permission_classes = [IsTokenValid, IsAuthenticated, IsAdminUser]

    def get(self, request, pk):
        user = get_object_or_404(User, pk=pk)
        user_one_to_one, created = Users.objects.get_or_create(user=user)
        user_serializer = UserDetailSerializer(user)

        user_serializer.data['rate'] = user_one_to_one.rate
        user_serializer.data['account_category'] = user_one_to_one.account_category
        user_serializer.data['is_accommodation_facility'] = user_one_to_one.is_accommodation_facility
        options = {
            'all_is_enabled': user_one_to_one.all_is_enabled,
            'ad_is_enabled': user_one_to_one.ad_is_enabled,
            'sale_is_enabled': user_one_to_one.sale_is_enabled,
            'coupon_is_enabled': user_one_to_one.coupon_is_enabled,
            'report_is_enabled': user_one_to_one.report_is_enabled,
            'plan_is_enabled': user_one_to_one.plan_is_enabled
        }

        user_serializer.data['options'] = options
        return Response(user_serializer.data)


class PasswordResetRequestView(APIView):
    def post(self, request):
        serializer = PasswordResetRequestSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']

            try:
                user = User.objects.get(email=email)

                if not user.is_active:
                    return Response({
                        'message': 'アカウントはまだ有効になっていません。',
                        'status': status.HTTP_400_BAD_REQUEST
                    }, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                return Response({
                    'message': 'メールでアカウントが見つかりません。',
                    'status': status.HTTP_404_NOT_FOUND
                }, status=status.HTTP_404_NOT_FOUND)
            token = default_token_generator.make_token(user)
            uid = urlsafe_base64_encode(force_bytes(user.id))
            url_frontend = os.getenv('URL_FRONTEND')

            reset_url = f'{url_frontend}/login/reset/{uid}/{token}'

            # Use improved email utility
            email_sent = send_password_reset_email(email, reset_url)

            if not email_sent:
                return Response({
                    'message': 'パスワードをリセットしたメールの送信に失敗しました。',
                    'status': status.HTTP_400_BAD_REQUEST
                }, status=status.HTTP_400_BAD_REQUEST)
            return Response({
                'message': 'パスワードをリセットしたメールを送信しました。',
                'status': status.HTTP_201_CREATED
            }, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class PasswordResetView(APIView):
    def post(self, request, uid, token):
        serializer = PasswordResetSerializer(data=request.data)
        if serializer.is_valid():
            try:
                user_id = force_str(urlsafe_base64_decode(uid))
                user = User.objects.get(id=user_id)
            except (TypeError, ValueError, OverflowError, User.DoesNotExist):
                user = None

            if user is not None and default_token_generator.check_token(user, token):
                password = serializer.validated_data['password']
                user.set_password(password)
                user.save()
                return Response({'message': 'パスワードが正常にリセットされました', 'status': status.HTTP_201_CREATED},
                                status=status.HTTP_201_CREATED)
            return Response({'message': 'パスワードを再設定しました。ログインしてもう一度お試しください。'}, status=status.HTTP_400_BAD_REQUEST)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UsersOptionView(APIView):
    def get(self, request, *args, **kwargs):
        account_option = ACCOUNT_OPTION,
        rate_option = RATE_OPTION

        data = {
            "account_option": account_option,
            "rate_option": rate_option
        }

        return Response({
            "data": data,
            "status": status.HTTP_200_OK
        }, status=status.HTTP_200_OK)


class UserIsActiveView(APIView):
    def get(self, request, email):
        user = get_object_or_404(User, email=email)
        # Assuming you've added an 'email' field to your User model
        is_active = user.is_active

        return Response({
            "is_active": is_active,
            "status": status.HTTP_200_OK
        }, status=status.HTTP_200_OK)


class UpdateProfileView(APIView):
    permission_classes = [IsTokenValid, IsAuthenticated]
    parser_classes = (MultiPartParser, FormParser)

    def put(self, request):
        serializer = UpdateProfileSerializer(data=request.data)

        if serializer.is_valid():
            user = request.user
            first_name = serializer.validated_data.get('first_name')
            last_name = serializer.validated_data.get('last_name')
            password = serializer.validated_data.get('password')
            avatar = request.data.get('avatar')
            email = request.data.get('email')
            user.first_name = first_name
            user.last_name = last_name
            user.email = email
            user.username = email
            if password:
                try:
                    validate_password(password, user=user)
                    user.password = make_password(password)
                except ValidationError as e:
                    return Response({
                        'error': e.messages,
                        'status': status.HTTP_400_BAD_REQUEST
                    }, status=status.HTTP_400_BAD_REQUEST)
            user.save()
            if avatar:
                users, created = Users.objects.get_or_create(user=user)
                users.avatar = avatar
                users.save()
            return Response({
                "message": "ユーザーが正常に編集されました。",
                "status": status.HTTP_200_OK
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                "message": serializer.errors,
                "status": status.HTTP_400_BAD_REQUEST
            }, status=status.HTTP_400_BAD_REQUEST)

class UpdatePartnerIdView(APIView):
    permission_classes = [IsTokenValid, IsAuthenticated]
    parser_classes = (MultiPartParser, FormParser)

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter('rakuten_id', openapi.IN_FORM, description="Rakuten ID", type=openapi.TYPE_STRING, required=False, nullable=True),
            openapi.Parameter('jaran_id', openapi.IN_FORM, description="Jaran ID", type=openapi.TYPE_STRING, required=False, nullable=True),
            openapi.Parameter('ikkyu_id', openapi.IN_FORM, description="Ikkyu ID", type=openapi.TYPE_STRING, required=False, nullable=True),
        ],
        responses={200: "Success", 400: "Bad Request"},
        operation_summary="Update Partner ID",
        operation_description="API to update partner IDs for the user.",
    )

    def put(self, request):
        serializer = UpdatePartnerIdSerializer(data=request.data)

        try:
            user = Users.objects.get(user_id=request.user.id)
        except User.DoesNotExist:
            return Response({
                "message": "ユーザーが見つかりません",
                "status": status.HTTP_404_NOT_FOUND
            }, status=status.HTTP_404_NOT_FOUND)

        if serializer.is_valid():
            rakuten_id = serializer.validated_data.get('rakuten_id')
            jaran_id = serializer.validated_data.get('jaran_id')
            ikkyu_id = serializer.validated_data.get('ikkyu_id')
            user.rakuten_id = rakuten_id
            user.jaran_id = jaran_id
            user.ikkyu_id = ikkyu_id
            user.save()
            return Response({
                "message": "ユーザーが正常に編集されました。",
                "status": status.HTTP_200_OK
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                "message": serializer.errors,
                "status": status.HTTP_400_BAD_REQUEST
            }, status=status.HTTP_400_BAD_REQUEST)

class UpdateNotificationEmailSettingView(APIView):
    permission_classes = [IsTokenValid, IsAuthenticated]
    parser_classes = (MultiPartParser, FormParser)

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter('is_sale_notification', openapi.IN_FORM, description="Sale notification", type=openapi.TYPE_BOOLEAN,
                              required=False, nullable=True),
            openapi.Parameter('is_planned_coupon_notification', openapi.IN_FORM, description="Planned coupon notification", type=openapi.TYPE_BOOLEAN,
                              required=False, nullable=True),
            openapi.Parameter('is_suggestion_from_micado', openapi.IN_FORM, description="Suggestion from micado", type=openapi.TYPE_BOOLEAN,
                              required=False, nullable=True),
        ],
        responses={200: "Success", 400: "Bad Request"},
        operation_summary="Update update notification settings",
        operation_description="API to update notification settings for the user.",
    )
    def put(self, request):
        try:
            user = Users.objects.get(user_id=request.user.id)
        except Users.DoesNotExist:  # Fix the typo in the exception handling
            return Response({
                "message": "ユーザーが見つかりません",
                "status": status.HTTP_404_NOT_FOUND
            }, status=status.HTTP_404_NOT_FOUND)

        boolean_fields = ['is_sale_notification', 'is_planned_coupon_notification', 'is_suggestion_from_micado']

        for field in boolean_fields:
            value = request.data.get(field)
            if value is not None:
                setattr(user, field, value.lower() == 'true')

        user.save()

        return Response({
            "message": "ユーザーが正常に編集されました。",
            "status": status.HTTP_200_OK
        }, status=status.HTTP_200_OK)

