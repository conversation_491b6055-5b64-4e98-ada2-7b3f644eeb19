"""
Email utilities for better deliverability and spam prevention
"""
import os
from django.core.mail import EmailMultiAlternatives


def send_activation_email(user_email, activation_url):
    """
    Send simple activation email with proper headers to improve deliverability
    """

    # Email subject - simple and clear
    subject = '[Levitt]メール認証のお願い'

    # Simple text content like the original
    text_content = f"""この度はLevittにご登録いただきありがとうございます。
お客様のLevittアカウントの仮登録が完了しました。

本登録を完了させるために24時間以内に下記のURLにアクセスしてください。
{activation_url}

万が一、ご登録いただいた覚えがない場合は、
Levittカスタマーサポートへご連絡ください。"""

    from_email = os.getenv('FROM_EMAIL')

    # Create email with proper headers for better deliverability
    email = EmailMultiAlternatives(
        subject=subject,
        body=text_content,
        from_email=f"Levitt <{from_email}>",  # Display name for better trust
        to=[user_email],
        headers={
            'Reply-To': from_email,
            'X-Mailer': 'Levitt Registration System',
            'X-Priority': '3',  # Normal priority
            'X-MSMail-Priority': 'Normal',
            'Importance': 'Normal',
            'List-Unsubscribe': f'<mailto:{from_email}?subject=Unsubscribe>',
            'Message-ID': f'<levitt-activation-{hash(user_email)}-{hash(activation_url)}@levitt.ai>',
        }
    )

    # Send email
    try:
        email.send(fail_silently=False)
        return True
    except Exception as e:
        print(f"Failed to send activation email: {str(e)}")
        return False


def send_password_reset_email(user_email, reset_url):
    """
    Send password reset email with proper formatting
    """
    subject = '[Levitt] パスワードリセットのお知らせ'
    
    text_content = f"""
Levittをご利用いただきありがとうございます。
パスワードのリセットを受け付けました。

以下のリンクをクリックし、新しいパスワードをご入力ください。

{reset_url}

このリンクは24時間後に無効になります。
心当たりがない場合は、このメールを無視してください。

© 2024 Levitt. All rights reserved.
"""
    
    from_email = os.getenv('FROM_EMAIL')
    
    email = EmailMultiAlternatives(
        subject=subject,
        body=text_content,
        from_email=f"Levitt <{from_email}>",
        to=[user_email],
        headers={
            'Reply-To': from_email,
            'X-Mailer': 'Levitt Password Reset System',
            'X-Priority': '3',
            'Message-ID': f'<levitt-reset-{hash(user_email)}-{hash(reset_url)}@levitt.ai>',
        }
    )
    
    try:
        email.send(fail_silently=False)
        return True
    except Exception as e:
        print(f"Failed to send password reset email: {str(e)}")
        return False


def send_admin_notification_email(user_email, user_first_name, user_last_name):
    """
    Send admin notification when user completes email verification
    """
    admin_email = os.getenv('ADMIN_NOTIFICATION_EMAIL', '<EMAIL>')
    subject = '[Levitt] 新規ユーザーがメール認証を完了しました'

    text_content = f"""{user_last_name}{user_first_name}様がメール認証を完了しました。
メールアドレス：{user_email}"""

    from_email = os.getenv('FROM_EMAIL')

    email = EmailMultiAlternatives(
        subject=subject,
        body=text_content,
        from_email=f"Levitt <{from_email}>",
        to=[admin_email],
        headers={
            'Reply-To': from_email,
            'X-Mailer': 'Levitt Admin Notification System',
            'X-Priority': '3',
            'Message-ID': f'<levitt-admin-{hash(user_email)}-{hash(user_first_name)}@levitt.ai>',
        }
    )

    try:
        email.send(fail_silently=False)
        return True
    except Exception as e:
        print(f"Failed to send admin notification email: {str(e)}")
        return False


def send_hotel_registration_email(user_email, first_name, last_name, hotel_name, ota_url):
    """
    Send hotel registration confirmation email
    """
    subject = 'Levittで施設の登録申請が来ました'

    text_content = f"""Levittで施設の登録申請が来ています。

申請者名
{last_name} {first_name}
{hotel_name}
{user_email}
{ota_url}"""

    from_email = os.getenv('FROM_EMAIL')

    email = EmailMultiAlternatives(
        subject=subject,
        body=text_content,
        from_email=f"Levitt <{from_email}>",
        to=[user_email],
        headers={
            'Reply-To': from_email,
            'X-Mailer': 'Levitt Hotel Registration System',
            'X-Priority': '3',
            'Message-ID': f'<levitt-hotel-{hash(user_email)}-{hash(hotel_name)}@levitt.ai>',
        }
    )

    try:
        email.send(fail_silently=False)
        return True
    except Exception as e:
        print(f"Failed to send hotel registration email: {str(e)}")
        return False
