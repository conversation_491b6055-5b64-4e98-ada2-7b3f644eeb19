from rest_framework.response import Response
from rest_framework import status
from .models import Users


class ApiCallLimitMixin:
    def check_api_call_limit(self, request):
        try:
            user = request.user
            user_profile = Users.objects.get(user=user)
            if user_profile.used_time >= user_profile.rate:
                return Response({"message": "API call limit reached"}, status=status.HTTP_429_TOO_MANY_REQUESTS)
        except Users.DoesNotExist:
            return Response({"message": "User profile not found"}, status=status.HTTP_404_NOT_FOUND)

        return None
