from django.core.management.base import BaseCommand
from users.models import Users
from django.conf import settings
from datetime import datetime


class Command(BaseCommand):
    help = 'Reset user rate'

    def handle(self, *args, **options):
        self.stdout.write(f'[{datetime.now()}] [#] Begin execute reset rate & used_time...')

        try:
            Users.objects.update(
                rate=settings.DEFAULT_RATE,
                used_time=0)
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'[{datetime.now()}] [!] Error: {e}'))
            return

        self.stdout.write(self.style.SUCCESS(f'[{datetime.now()}] [#] Successfully executed!'))
