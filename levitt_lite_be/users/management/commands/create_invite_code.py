from django.core.management.base import BaseCommand
import random
import string
from users.models import Users

INVITE_CODE_LENGTH = 16
class Command(BaseCommand):
    help = 'Create invite code'

    def handle(self, *args, **options):
        # update user where invite_code is null
        self.stdout.write('[#] Begin execute...')
        try:
            for user in Users.objects.filter(invite_code=None):

                user_invite_code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=INVITE_CODE_LENGTH)) + str(user.id)
                user.invite_code = user_invite_code
                user.save()
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'[!] Error: {e}'))
            return
        self.stdout.write(self.style.SUCCESS('[#] Successfully executed!'))
