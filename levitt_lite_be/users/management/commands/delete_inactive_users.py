from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import timedelta, datetime


class Command(BaseCommand):
    help = 'Delete all users after 1 day of inactive mail'

    def handle(self, *args, **options):
        self.stdout.write('[#] Begin execute delete user...')
        self.stdout.write(f'[{datetime.now()}] [#] Begin execute delete user...')
        one_day_ago = timezone.now() - timedelta(days=1)
        users_to_delete = User.objects.filter(is_active=0, date_joined__lt=one_day_ago)
        count = users_to_delete.count()

        if count > 0:
            users_to_delete.delete()
            self.stdout.write(self.style.SUCCESS(f'[{datetime.now()}] Successfully deleted {count} user(s)'))
        else:
            self.stdout.write(self.style.SUCCESS(f'[{datetime.now()}] No inactive users to delete'))

        self.stdout.write(self.style.SUCCESS(f'[{datetime.now()}] [#] Successfully delete executed!'))
