# Generated by Django 5.0 on 2023-12-21 07:08

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0023_users_ikkyu_id_users_jaran_id_users_rakuten_id'),
    ]

    operations = [
        migrations.AddField(
            model_name='users',
            name='is_planned_coupon_notification',
            field=models.BooleanField(default=False, null=True, verbose_name='企画型クーポン通知'),
        ),
        migrations.AddField(
            model_name='users',
            name='is_sale_notification',
            field=models.BooleanField(default=False, null=True, verbose_name='セール開催通知'),
        ),
        migrations.AddField(
            model_name='users',
            name='is_suggestion_from_micado',
            field=models.BooleanField(default=False, null=True, verbose_name='micado++からの提案'),
        ),
    ]
