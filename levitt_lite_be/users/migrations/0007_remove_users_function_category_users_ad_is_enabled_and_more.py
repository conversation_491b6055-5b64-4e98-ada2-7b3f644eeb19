# Generated by Django 4.2.6 on 2023-10-18 04:49

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0006_remove_users_accommodation_name"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="users",
            name="function_category",
        ),
        migrations.AddField(
            model_name="users",
            name="ad_is_enabled",
            field=models.BooleanField(default=False, verbose_name="機能分類・広告提案"),
        ),
        migrations.AddField(
            model_name="users",
            name="all_is_enabled",
            field=models.BooleanField(default=False, verbose_name="機能分類・すべての機能"),
        ),
        migrations.AddField(
            model_name="users",
            name="coupon_is_enabled",
            field=models.BooleanField(default=False, verbose_name="機能分類・クーポン提案"),
        ),
        migrations.AddField(
            model_name="users",
            name="plan_is_enabled",
            field=models.BooleanField(default=False, verbose_name="機能分類・プラン提案"),
        ),
        migrations.AddField(
            model_name="users",
            name="report_is_enabled",
            field=models.BooleanField(default=False, verbose_name="機能分類・レポート機能提案"),
        ),
        migrations.AddField(
            model_name="users",
            name="sale_is_enabled",
            field=models.BooleanField(default=False, verbose_name="機能分類・セール提案"),
        ),
        migrations.AlterField(
            model_name="users",
            name="account_category",
            field=models.CharField(
                blank=True, max_length=100, null=True, verbose_name="アカウント分類"
            ),
        ),
        migrations.AlterField(
            model_name="users",
            name="rate",
            field=models.CharField(
                blank=True, max_length=100, null=True, verbose_name="発行可能回数"
            ),
        ),
    ]
