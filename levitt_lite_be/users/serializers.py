from rest_framework import serializers
from django.contrib.auth.models import User
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from .models import Users, ACCOUNT_OPTION
from hotels.models import Hotels
import os
from dotenv import load_dotenv

load_dotenv()


message_not_blank = 'このフィールドは空白にできません。'


class ActivateUserSerializer(serializers.Serializer):
    uid = serializers.CharField()
    token = serializers.CharField()
    first_name = serializers.CharField(required=False)
    last_name = serializers.Char<PERSON>ield(required=False)
    password = serializers.CharField(required=False)
    hotel_id = serializers.IntegerField(required=False, allow_null=True)


class UserSerializer(serializers.ModelSerializer):
    hotel_name = serializers.SerializerMethodField()
    rate = serializers.SerializerMethodField()
    used_time = serializers.SerializerMethodField()
    avatar = serializers.SerializerMethodField(source='users.avatar')
    rakuten_id = serializers.SerializerMethodField(source='users.rakuten_id')
    jaran_id = serializers.SerializerMethodField(source='users.jaran_id')
    ikkyu_id = serializers.SerializerMethodField(source='users.ikkyu_id')
    is_sale_notification = serializers.SerializerMethodField(source='users.is_sale_notification')
    is_planned_coupon_notification = serializers.SerializerMethodField(source='users.is_planned_coupon_notification')
    is_suggestion_from_micado = serializers.SerializerMethodField(source='users.is_suggestion_from_micado')
    invite_code = serializers.SerializerMethodField(source='users.invite_code')

    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'is_active', 'is_superuser', 'hotel_name', 'rate', 'used_time',
            'avatar', 'rakuten_id', 'jaran_id', 'ikkyu_id', 'is_sale_notification', 'is_planned_coupon_notification',
            'is_suggestion_from_micado', 'invite_code'
        ]

    def get_avatar(self, user):
        url_backend = os.getenv('URL_BACKEND')
        try:
            users = Users.objects.get(user=user)
            if users and users.avatar:
                return url_backend + users.avatar.url
            else:
                return url_backend + '/media/avatars/avatar_default.png'
        except Users.DoesNotExist:
            return url_backend + '/media/avatars/avatar_default.png'

    def get_hotel_name(self, user):
        try:
            hotel_info = Hotels.objects.get(users__user=user)
            return hotel_info.name
        except Hotels.DoesNotExist:
            return None

    def get_rate(self, user):
        try:
            users = Users.objects.get(user=user)
            return users.rate or 0
        except Users.DoesNotExist:
            return None

    def get_used_time(self, user):
        try:
            users = Users.objects.get(user=user)
            return users.used_time or 0
        except Users.DoesNotExist:
            return 0
    def get_rakuten_id(self, user):
        try:
            users = Users.objects.get(user=user)
            return users.rakuten_id
        except Users.DoesNotExist:
            return None
    def get_jaran_id(self, user):
        try:
            users = Users.objects.get(user=user)
            return users.jaran_id
        except Users.DoesNotExist:
            return None
    def get_ikkyu_id(self, user):
        try:
            users = Users.objects.get(user=user)
            return users.ikkyu_id
        except Users.DoesNotExist:
            return None

    def get_is_sale_notification(self, user):
        try:
            users = Users.objects.get(user=user)
            return users.is_sale_notification
        except Users.DoesNotExist:
            return None

    def get_is_planned_coupon_notification(self, user):
        try:
            users = Users.objects.get(user=user)
            return users.is_planned_coupon_notification
        except Users.DoesNotExist:
            return None

    def get_is_suggestion_from_micado(self, user):
        try:
            users = Users.objects.get(user=user)
            return users.is_suggestion_from_micado
        except Users.DoesNotExist:
            return None

    def get_invite_code(self, user):
        try:
            users = Users.objects.get(user=user)
            return users.invite_code
        except Users.DoesNotExist:
            return None

class HotelsSerializer(serializers.ModelSerializer):
    class Meta:
        model = Hotels
        fields = '__all__'


class UserDetailSerializer(serializers.ModelSerializer):
    rate = serializers.CharField(source='users.rate', read_only=True)
    account_category = serializers.CharField(source='users.account_category', read_only=True)
    options = serializers.SerializerMethodField()
    is_accommodation_facility = serializers.BooleanField(source='users.is_accommodation_facility', read_only=True)

    def get_options(self, obj):
        return {
            'all_is_enabled': obj.users.all_is_enabled,
            'ad_is_enabled': obj.users.ad_is_enabled,
            'sale_is_enabled': obj.users.sale_is_enabled,
            'coupon_is_enabled': obj.users.coupon_is_enabled,
            'report_is_enabled': obj.users.report_is_enabled,
            'plan_is_enabled': obj.users.plan_is_enabled
        }

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data['options'] = self.get_options(instance)
        return data
    hotel = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            'id',
            'username',
            'first_name',
            'last_name',
            'email',
            'is_active',
            'rate',
            'account_category',
            'is_accommodation_facility',
            'hotel',
            'options'
        ]

    def get_hotel(self, user):
        try:
            hotel_info = Hotels.objects.get(users__user=user)
            hotel_serializer = HotelsSerializer(hotel_info)
            return hotel_serializer.data
        except Hotels.DoesNotExist:
            return None


class CreateUserSerializer(serializers.Serializer):
    email = serializers.EmailField(
        error_messages={
            'blank': message_not_blank,
            'invalid': '有効なメールアドレスを入力してください。'
        }
    )
    is_superuser = serializers.BooleanField(
        required=False,
        error_messages={
            'invalid': '有効なブール値である必要があります。'
        }
    )

    def validate_email(self, value):
        """
        Check if the email address already exists in the database.
        """
        if User.objects.filter(email=value, is_active=1).exists():
            raise serializers.ValidationError('入力されたメールアドレスは既に存在します。再度ご確認ください。 ')
        return value


class CreateAccountSerializer(serializers.Serializer):
    email = serializers.EmailField()
    is_active = serializers.BooleanField(required=False)
    account_category = serializers.ChoiceField(choices=ACCOUNT_OPTION)
    rate = serializers.IntegerField(required=False)

    def validate_account_category(self, value):
        if value not in dict(ACCOUNT_OPTION):
            raise serializers.ValidationError('Invalid account category')
        return value

    def validate_email(self, value):
        """
        Check if the email address already exists in the database.
        """
        if User.objects.filter(email=value, is_active=1).exists():
            raise serializers.ValidationError(
                '入力されたメールアドレスは既に存在します。再度ご確認ください。 ')
        return value


class UpdateUserSerializer(serializers.Serializer):
    first_name = serializers.CharField(
        max_length=150,
        error_messages={
            'blank': message_not_blank,
            'max_length': 'このフィールドは150文字以下でなければなりません。'
        }
    )

    last_name = serializers.CharField(
        max_length=150,
        error_messages={
            'blank': message_not_blank,
            'max_length': 'このフィールドは150文字以下でなければなりません。'
        }
    )

    account_category = serializers.ChoiceField(choices=ACCOUNT_OPTION, required=False)
    rate = serializers.CharField(required=True)
    all_is_enabled = serializers.BooleanField(required=True)
    plan_is_enabled = serializers.BooleanField(required=True)
    ad_is_enabled = serializers.BooleanField(required=True)
    coupon_is_enabled = serializers.BooleanField(required=True)
    report_is_enabled = serializers.BooleanField(required=True)
    sale_is_enabled = serializers.BooleanField(required=True)
    is_accommodation_facility = serializers.BooleanField(required=True)
    hotel_id = serializers.CharField(required=False)

    def validate_hotel_id(self, value):
        if value in [None, '', 'null']:
            return None
        try:
            return int(value)
        except ValueError:
            raise serializers.ValidationError("有効な整数またはnullが必要です。")

    def validate_first_name(self, value):
        if not value:
            raise serializers.ValidationError('このフィールドは必須です。')
        return value

    def validate_last_name(self, value):
        if not value:
            raise serializers.ValidationError('このフィールドは必須です。')
        return value

    def validate_rate(self, value):
        if not value:
            raise serializers.ValidationError('このフィールドは必須です。')
        return value


class PasswordResetRequestSerializer(serializers.Serializer):
    email = serializers.EmailField()


class PasswordResetSerializer(serializers.Serializer):
    password = serializers.CharField(write_only=True)


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)
        token['is_superadmin'] = user.is_superuser
        return token

    def validate(self, attrs):
        data = super().validate(attrs)
        data['is_superuser'] = self.user.is_superuser
        return data


class UpdateProfileSerializer(serializers.Serializer):
    first_name = serializers.CharField(required=False)
    last_name = serializers.CharField(required=False)
    password = serializers.CharField(required=False)
    avatar = serializers.ImageField(required=False)

class UpdatePartnerIdSerializer(serializers.Serializer):
    rakuten_id = serializers.CharField(required=False)
    jaran_id = serializers.CharField(required=False)
    ikkyu_id = serializers.CharField(required=False)

    def validate_rakuten_id(self, value):
        if not value.isdigit() or len(value) < 3 or len(value) > 6:
            raise serializers.ValidationError("Rakuten ID must be a 3~6-digit number.")
        return value

    def validate_jaran_id(self, value):
        if not value.isdigit() or len(value) != 6:
            raise serializers.ValidationError("Jaran ID must be a 6-digit number.")
        return value

    def validate_ikkyu_id(self, value):
        if not value.isdigit() or len(value) != 8:
            raise serializers.ValidationError("Ikkyu ID must be an 8-digit number.")
        return value
