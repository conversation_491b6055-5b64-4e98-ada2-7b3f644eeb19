from django.db import models
from django.contrib.auth.models import User
from hotels.models import Hotels

ACCOUNT_OPTION = [
    (1, "無料アカウント"),
    (2, "Basicプラン"),
    (3, "エンタープライズプラン"),
]

RATE_OPTION = [
    (1, 5),
    (2, 10),
    (3, 15),
    (4, 20),
    (5, 25),
    (6, 30),
]

class Users(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    hotel = models.ForeignKey(Hotels, on_delete=models.CASCADE, null=True)
    account_category = models.IntegerField(choices=ACCOUNT_OPTION, default=1, null=True, verbose_name="アカウント分類")
    all_is_enabled = models.BooleanField(default=False, verbose_name="機能分類・すべての機能")
    plan_is_enabled = models.BooleanField(default=False, verbose_name="機能分類・プラン提案")
    ad_is_enabled = models.BooleanField(default=False, verbose_name="機能分類・広告提案")
    sale_is_enabled = models.BooleanField(default=False, verbose_name="機能分類・セール提案")
    coupon_is_enabled = models.BooleanField(default=False, verbose_name="機能分類・クーポン提案")
    report_is_enabled = models.BooleanField(default=False, verbose_name="機能分類・レポート機能提案")
    is_accommodation_facility = models.BooleanField(default=False, verbose_name="宿泊施設であること確認した")
    avatar = models.ImageField(upload_to='avatars/', null=True, blank=True)
    used_time = models.IntegerField(default=0, null=True, verbose_name="発行可能回数使用済")
    rate = models.IntegerField(default=0, null=True, verbose_name="発行可能回数")
    rakuten_id = models.CharField(max_length=6, null=True, blank=True)
    jaran_id = models.CharField(max_length=6, null=True, blank=True)
    ikkyu_id = models.CharField(max_length=8, null=True, blank=True)
    is_sale_notification = models.BooleanField(default=False, null=True, verbose_name="セール開催通知")
    is_planned_coupon_notification = models.BooleanField(default=False, null=True, verbose_name="企画型クーポン通知")
    is_suggestion_from_micado = models.BooleanField(default=False, null=True, verbose_name="micado++からの提案")
    invite_code = models.CharField(max_length=50, null=True, blank=True, verbose_name="招待コード")

    class Meta:
        db_table = 'users'


class BlackListedToken(models.Model):
    token = models.CharField(max_length=500)
    user = models.ForeignKey(User, related_name="token_user", on_delete=models.CASCADE)
    timestamp = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ("token", "user")
