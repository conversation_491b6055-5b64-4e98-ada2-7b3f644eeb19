# Generated by Django 4.2.6 on 2023-10-17 09:47

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Ota",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "ota",
                    models.IntegerField(
                        blank=True,
                        choices=[(1, "楽天トラベル"), (2, "じゃらん"), (3, "一休")],
                        null=True,
                        verbose_name="OTA",
                    ),
                ),
                (
                    "title",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="タイトル"
                    ),
                ),
                (
                    "category",
                    models.IntegerField(
                        blank=True,
                        choices=[(1, "クーポン"), (2, "セール"), (3, "キャンペーン")],
                        null=True,
                        verbose_name="カテゴリー",
                    ),
                ),
                (
                    "participation_deadline",
                    models.DateField(blank=True, null=True, verbose_name="公開日"),
                ),
            ],
            options={
                "db_table": "otas",
            },
        ),
    ]
