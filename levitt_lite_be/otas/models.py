from django.db import models

OTA_OPTION = [
    (1, "楽天トラベル"),
    (2, "じゃらん"),
    (3, "一休"),
]

CATEGORY_OPTION = [
    (1, "クーポン"),
    (2, "セール"),
    (3, "キャンペーン"),
]

class Ota(models.Model):
    ota = models.IntegerField(choices=OTA_OPTION, null=True, blank=True, verbose_name="OTA")
    title = models.CharField(max_length=255, null=True, blank=True, verbose_name="タイトル")
    category = models.IntegerField(choices=CATEGORY_OPTION, null=True, blank=True, verbose_name="カテゴリー")
    participation_deadline = models.DateField(null=True, blank=True, verbose_name="公開日")

    class Meta:
        db_table = 'otas'

    def __str__(self):
        return self.title
