# notifications/serializers.py
from rest_framework import serializers
from .models import Ota, OTA_OPTION, CATEGORY_OPTION


class OtaSerializer(serializers.ModelSerializer):
    ota = serializers.ChoiceField(choices=OTA_OPTION, required=True)
    title = serializers.CharField(max_length=255, required=True)
    category = serializers.ChoiceField(choices=CATEGORY_OPTION, required=True)
    participation_deadline = serializers.DateField(required=True)

    class Meta:
        model = Ota
        fields = '__all__'
