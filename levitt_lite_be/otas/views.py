from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import viewsets
from rest_framework.response import Response
from rest_framework.views import APIView
from starlette import status
from .models import Ota, OTA_OPTION, CATEGORY_OPTION
from .serializers import OtaSerializer
from rest_framework.pagination import PageNumberPagination
from users.views import IsAdminUser, IsTokenValid, IsAuthenticated


class CustomPaginationClassOtas(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'


class OtaViewSet(viewsets.ModelViewSet):
    queryset = Ota.objects.all()
    serializer_class = OtaSerializer

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        if self.action in ["create", "update", "destroy"]:
            permission_classes = [IsAdminUser, IsTokenValid, IsAuthenticated]
        else:
            permission_classes = [IsTokenValid, IsAuthenticated]
        return [permission() for permission in permission_classes]

    def search_param(self):
        queryset = Ota.objects.all()
        category = self.request.query_params.get('category', None)
        if category is not None:
            queryset = queryset.filter(category=category)

        limit = self.request.query_params.get('limit', None)
        offset = self.request.query_params.get('offset', None)

        if limit is not None and offset is not None:
            limit = int(limit)
            offset = int(offset)
            queryset = queryset[offset:offset+limit]

        return queryset

    @swagger_auto_schema(manual_parameters=[
        openapi.Parameter('is_admin_interface', openapi.IN_QUERY, description="Your parameter description",
                          type=openapi.TYPE_STRING),
        openapi.Parameter('category', openapi.IN_QUERY, description="Your parameter description",
                          type=openapi.TYPE_STRING),
        openapi.Parameter('limit', openapi.IN_QUERY, description="Your parameter description",
                          type=openapi.TYPE_STRING),
    ])
    def list(self, request, *args, **kwargs):
        queryset = self.search_param()

        isAdminInterface = self.request.query_params.get('is_admin_interface', False)
        if isAdminInterface:
            paginator = CustomPaginationClassOtas()
            page = paginator.paginate_queryset(queryset, request)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                custom_data = {
                    "message": "Otas fetched successfully",
                    "data": serializer.data,
                    "total_pages": paginator.page.paginator.num_pages,
                    "status": status.HTTP_200_OK
                }
                return paginator.get_paginated_response(custom_data)
        else:
            # Serialize the queryset
            serializer = self.get_serializer(queryset, many=True)
            # Customize the response data as needed
            custom_data = {
                "message": "Otas fetched successfully",
                "data": serializer.data,
                "status": status.HTTP_200_OK
            }
            return Response(custom_data)

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)

        if serializer.is_valid():
            serializer.save()

            return Response({
                "message": "OTAが正常に作成されました。",
                "status": status.HTTP_201_CREATED
            }, status=status.HTTP_201_CREATED)
        else:
            return Response({
                "message": "作成に失敗しました。",
                "status": status.HTTP_400_BAD_REQUEST
            }, status=status.HTTP_400_BAD_REQUEST)

    def update(self, request, pk=None):
        ota = self.get_object()
        serializer = self.get_serializer(ota, data=request.data, partial=True)

        if serializer.is_valid():
            serializer.save()
            return Response({
                "message": "OTAが正常に編集されました。",
                "status": status.HTTP_200_OK
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                "message": "編集に失敗しました。",
                "status": status.HTTP_400_BAD_REQUEST
            }, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, pk=None):
        try:
            ota = self.get_object()
            ota.delete()
            return Response({
                "message": "OTAが正常に削除されました。",
                "status": status.HTTP_204_NO_CONTENT
            }, status=status.HTTP_204_NO_CONTENT)
        except Exception as e:
            return Response({
                "message": "削除に失敗しました。",
                "status": status.HTTP_400_BAD_REQUEST
            }, status=status.HTTP_400_BAD_REQUEST)


class OtaSelectOptionsView(APIView):
    def get(self, request, *args, **kwargs):
        ota_options = OTA_OPTION
        category_options = CATEGORY_OPTION

        data = {
            "ota_options": ota_options,
            "category_options": category_options
        }

        return Response({
            "data": data,
            "status": status.HTTP_200_OK
        }, status=status.HTTP_200_OK)
