ALLOWED_HOSTS=locahost,127.0.0.1,127.0.0.1:3000,locahost:3000,api.levitt.ai,levitt.ai
CORS_ORIGIN_WHITELIST=http://localhost:3000,http://127.0.0.1:3000,https://levitt.ai
SECRET_KEY='secretkey'
DB_HOST='localhost'
DB_PORT='3306'
DB_USER='username'
DB_PASSWORD='password'
DB_NAME='database'

MARQO_INDEX_NAME='index_master'
MARQO_VECTOR_URL='http://localhost:8882'

# OPENAPI KEY DEFINITION
OPENAI_API_KEY='open ai key'
EMBED_MODEL="text-embedding-ada-002"

# Mail settings
EMAIL_HOST='email host'
EMAIL_HOST_USER='email host user'
EMAIL_HOST_PASSWORD='email host password'
EMAIL_PORT='587'
EMAIL_USE_TLS=True
FROM_EMAIL='email'
ADMIN_NOTIFICATION_EMAIL='<EMAIL>'

# Extend URL
URL_BACKEND='https://api.levitt.ai'
SWAGGER_CLIENT_URL='https://api.levitt.ai'

# Set rate call api default
DEFAULT_RATE=5

# Set assistant api key and id
OPENAI_ASSISTANT_API_KEY='sk-*******'
OPENAI_ASSISTANT_API_ID='asst_*******'

# Set salesforce information
SALESFORCE_CLIENT_ID='your_client_id_here'
SALESFORCE_CLIENT_SECRET='your_client_secret_here'
SALESFORCE_USERNAME='your_salesforce_username_here'
SALESFORCE_PASSWORD='your_salesforce_password_here'
SALESFORCE_LOGIN_URL='your_salesforce_login_url_here'
SALESFORCE_API_URL='your_salesforce_api_url_here'

# Set slack webhook url
SLACK_WEBHOOK_URL='YOUR_SLACK_WEBHOOK_URL'
