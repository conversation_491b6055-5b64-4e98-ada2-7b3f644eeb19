from .common import SITE_CHOICES
from django.db import models
from django.contrib.auth.models import User
import sys
sys.path.append('./hotels')


class Area(models.Model):
    name = models.TextField(verbose_name='エリア')

    class Meta:
        db_table = 'areas'


class SubAreas(models.Model):
    name = models.TextField(verbose_name='サブエリア')
    area = models.ForeignKey(Area, on_delete=models.CASCADE)

    class Meta:
        db_table = 'sub_areas'


class Hotels(models.Model):
    name = models.TextField(verbose_name='宿泊施設名', blank=True, null=True)
    address = models.TextField(verbose_name='住所', blank=True, null=True)
    number_of_room = models.IntegerField(verbose_name='部屋数', blank=True, null=True)
    type = models.TextField(verbose_name='タイプ', blank=True, null=True)
    in_site_id = models.TextField(verbose_name='OTAのURL_id', blank=True, null=True)
    area = models.TextField(verbose_name='エリア', blank=True, null=True)
    first_name = models.TextField(verbose_name='名', blank=True, null=True)
    last_name = models.TextField(verbose_name='姓', blank=True, null=True)
    is_active = models.BooleanField(default=False)
    is_user_registered = models.BooleanField(default=False)
    email_registration = models.TextField(verbose_name='メール登録', blank=True, null=True)
    postal_code = models.TextField(verbose_name='郵便番号', blank=True, null=True)
    competition_registrations = models.ManyToManyField(User, through='CompetitionRegistration')
    rate = models.TextField(verbose_name='レート', blank=True, null=True)

    class Meta:
        db_table = 'hotels'


class GptPlans(models.Model):
    hotel = models.ForeignKey(Hotels, on_delete=models.CASCADE)
    name = models.TextField(verbose_name='タイトル')
    term = models.TextField(verbose_name='期間')
    description = models.TextField(verbose_name='説明文')

    class Meta:
        db_table = 'gpt_plans'


class Rooms(models.Model):
    hotel = models.ForeignKey(Hotels, on_delete=models.CASCADE)
    name = models.TextField(verbose_name='部屋の名称')
    size = models.TextField(verbose_name='広さ')
    type = models.TextField(verbose_name='部屋タイプ')
    count = models.TextField(verbose_name='室数')
    price = models.TextField(verbose_name='価格')

    class Meta:
        db_table = 'rooms'


class Sales(models.Model):
    name = models.TextField(verbose_name='タイトル')
    term = models.TextField(verbose_name='期間')
    description = models.TextField(verbose_name='説明文')
    budget = models.TextField(verbose_name='料金')

    class Meta:
        db_table = 'sales'


class Answer(models.Model):
    answer = models.TextField(verbose_name='答え')
    estimate_code = models.TextField(verbose_name='推定コード')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='作成日')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新日')

    class Meta:
        db_table = 'answer'


class PlanContents(models.Model):
    gpt_plan = models.ForeignKey(GptPlans, on_delete=models.CASCADE)
    room_type = models.TextField(verbose_name='タイトル')
    meal = models.TextField(verbose_name='期間')
    budget = models.TextField(verbose_name='説明文')

    class Meta:
        db_table = 'plan_contents'


class CompetitionRegistration(models.Model):
    hotel = models.ForeignKey(Hotels, on_delete=models.CASCADE)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'competition_registrations'
        unique_together = ('hotel', 'user')
