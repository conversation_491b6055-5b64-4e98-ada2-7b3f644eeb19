from rest_framework import serializers
from .models import Hotels
from .models import CompetitionRegistration


class HotelSerializer(serializers.ModelSerializer):
    name = serializers.CharField(required=True)
    address = serializers.CharField(required=True)
    in_site_id = serializers.CharField(required=True)
    first_name = serializers.CharField(required=True)
    last_name = serializers.CharField(required=True)

    class Meta:
        model = Hotels
        fields = '__all__'

class ApplyRegistrationHotelSerializer(serializers.ModelSerializer):
    name = serializers.CharField(required=True)
    address = serializers.CharField(required=True)

    class Meta:
        model = Hotels
        fields = '__all__'


class CompetitionRegistrationSerializer(serializers.ModelSerializer):
    hotel_name = serializers.CharField(source='hotel.name', read_only=True)
    number_of_room = serializers.IntegerField(source='hotel.number_of_room', read_only=True)
    address = serializers.CharField(source='hotel.address', read_only=True)
    postal_code = serializers.Char<PERSON><PERSON>(source='hotel.postal_code', read_only=True)
    rate = serializers.Char<PERSON>ield(source='hotel.rate', read_only=True)
    hotel_id = serializers.CharField(source='hotel.id', read_only=True)

    class Meta:
        model = CompetitionRegistration
        fields = '__all__'
        read_only_fields = ['user']
