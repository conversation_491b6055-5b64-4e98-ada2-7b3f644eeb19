# Generated by Django 4.2.6 on 2023-11-18 02:41

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('hotels', '0020_recommendplan'),
    ]

    operations = [
        migrations.CreateModel(
            name='CompetitionRegistration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('hotel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='hotels.hotels')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'competition_registrations',
                'unique_together': {('hotel', 'user')},
            },
        ),
        migrations.AddField(
            model_name='hotels',
            name='competition_registrations',
            field=models.ManyToManyField(through='hotels.CompetitionRegistration', to=settings.AUTH_USER_MODEL),
        ),
    ]
