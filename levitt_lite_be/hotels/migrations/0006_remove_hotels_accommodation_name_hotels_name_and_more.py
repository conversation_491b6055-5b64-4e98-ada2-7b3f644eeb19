# Generated by Django 4.2.4 on 2023-10-12 03:35

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("hotels", "0005_rename_frist_name_hotels_first_name"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="hotels",
            name="accommodation_name",
        ),
        migrations.AddField(
            model_name="hotels",
            name="name",
            field=models.TextField(blank=True, null=True, verbose_name="宿泊施設名"),
        ),
        migrations.AlterField(
            model_name="hotels",
            name="address",
            field=models.TextField(blank=True, null=True, verbose_name="住所"),
        ),
        migrations.AlterField(
            model_name="hotels",
            name="first_name",
            field=models.TextField(blank=True, null=True, verbose_name="名"),
        ),
        migrations.AlterField(
            model_name="hotels",
            name="in_site_id",
            field=models.CharField(
                blank=True, max_length=20, null=True, verbose_name="OTAのURL_id"
            ),
        ),
        migrations.AlterField(
            model_name="hotels",
            name="last_name",
            field=models.TextField(blank=True, null=True, verbose_name="姓"),
        ),
        migrations.AlterField(
            model_name="hotels",
            name="number_of_room",
            field=models.IntegerField(blank=True, null=True, verbose_name="部屋数"),
        ),
        migrations.AlterField(
            model_name="hotels",
            name="sub",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="hotels.subareas",
            ),
        ),
        migrations.AlterField(
            model_name="hotels",
            name="type",
            field=models.IntegerField(
                blank=True,
                choices=[(1, "Rakuten"), (2, "Jalan")],
                null=True,
                verbose_name="タイプ",
            ),
        ),
    ]
