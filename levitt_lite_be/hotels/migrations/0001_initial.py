# Generated by Django 4.2.4 on 2023-08-07 04:02

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Area',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.TextField(verbose_name='エリア')),
            ],
            options={
                'db_table': 'areas',
            },
        ),
        migrations.CreateModel(
            name='Hotels',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.TextField(verbose_name='施設名')),
                ('address', models.TextField(verbose_name='住所')),
                ('number_of_room', models.IntegerField(verbose_name='部屋数')),
                ('type', models.IntegerField(choices=[(1, 'Rakuten'), (2, 'Jalan')], verbose_name='タイプ')),
                ('in_site_id', models.CharField(max_length=20, verbose_name='OTAのURL_id')),
            ],
            options={
                'db_table': 'hotels',
            },
        ),
        migrations.CreateModel(
            name='Sales',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.TextField(verbose_name='タイトル')),
                ('term', models.TextField(verbose_name='期間')),
                ('description', models.TextField(verbose_name='説明文')),
                ('buget', models.TextField(verbose_name='料金')),
            ],
            options={
                'db_table': 'sales',
            },
        ),
        migrations.CreateModel(
            name='SubAreas',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.TextField(verbose_name='サブエリア')),
                ('area', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='hotels.area')),
            ],
            options={
                'db_table': 'sub_areas',
            },
        ),
        migrations.CreateModel(
            name='Rooms',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.TextField(verbose_name='部屋の名称')),
                ('size', models.TextField(verbose_name='広さ')),
                ('type', models.TextField(verbose_name='部屋タイプ')),
                ('count', models.TextField(verbose_name='室数')),
                ('price', models.TextField(verbose_name='価格')),
                ('hotel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='hotels.hotels')),
            ],
            options={
                'db_table': 'rooms',
            },
        ),
        migrations.CreateModel(
            name='Plans',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.TextField(verbose_name='タイトル')),
                ('term', models.TextField(verbose_name='期間')),
                ('description', models.TextField(verbose_name='説明文')),
                ('buget', models.TextField(verbose_name='料金')),
                ('hotel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='hotels.hotels')),
            ],
            options={
                'db_table': 'plans',
            },
        ),
        migrations.AddField(
            model_name='hotels',
            name='sub',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='hotels.subareas'),
        ),
    ]
