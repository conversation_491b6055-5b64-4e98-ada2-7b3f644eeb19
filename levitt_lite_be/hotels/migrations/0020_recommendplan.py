# Generated by Django 4.2.6 on 2023-11-03 09:11

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("hotels", "0019_delete_recommendplan"),
    ]

    operations = [
        migrations.CreateModel(
            name="RecommendPlan",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.TextField(null=True, verbose_name="タイトル")),
                ("description", models.TextField(null=True, verbose_name="説明文")),
                ("term", models.TextField(null=True, verbose_name="期間")),
                ("room_type", models.TextField(null=True, verbose_name="教室タイプ")),
                ("perks", models.TextField(null=True, verbose_name="特典")),
                ("meal", models.TextField(null=True, verbose_name="食事")),
                ("budget", models.TextField(null=True, verbose_name="参考販売価格")),
                ("ota_sell", models.TextField(null=True, verbose_name="販売するOTAを選ぶ")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="作成日"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "recommend_plans",
            },
        ),
    ]
