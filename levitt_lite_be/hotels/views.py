from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import viewsets, response, status
from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response
from django.db.models import Q
from users.views import IsAdminUser, IsTokenValid, IsAuthenticated
from .models import Hotels, CompetitionRegistration
from .serializers import HotelSerializer, ApplyRegistrationHotelSerializer, CompetitionRegistrationSerializer
from django.db.utils import IntegrityError
from django.contrib.auth.models import AnonymousUser
from users.email_utils import send_hotel_registration_email
import os
from dotenv import load_dotenv
load_dotenv()


class CustomPaginationClassHotels(PageNumberPagination):
    page_size = 20
    page_size_query_param = 'page_size'

    def get_paginated_response(self, data):
        backend_url = os.getenv('URL_BACKEND')
        next_link = self.get_next_link()
        previous_link = self.get_previous_link()

        custom_next_link = ''
        custom_previous_link = ''
        if backend_url.startswith('https://'):
            if next_link:
                custom_next_link = next_link.replace('http://', 'https://')

            if previous_link:
                custom_previous_link = previous_link.replace('http://', 'https://')
        else:
            custom_next_link = next_link
            custom_previous_link = previous_link

        if custom_next_link:
            custom_next_link = custom_next_link.replace(self.request.build_absolute_uri('/'), backend_url + '/')
        if custom_previous_link:
            custom_previous_link = custom_previous_link.replace(self.request.build_absolute_uri('/'), backend_url + '/')

        return Response({
            'next': custom_next_link,
            'previous': custom_previous_link,
            'count': self.page.paginator.count,
            'results': data
        })


class HotelViewSet(viewsets.ModelViewSet):
    serializer_class = HotelSerializer
    queryset = Hotels.objects.filter(is_active=True)

    def get_permissions(self):
        if self.action == 'update':
            permission_classes = [IsAdminUser]
        else:
            permission_classes = [IsTokenValid]
        return [permission() for permission in permission_classes]

    def search_param(self):
        search_param = self.request.query_params.get('search_param', '')
        queryset = self.queryset.filter(
            Q(name__contains=search_param) | Q(address__contains=search_param)
        ).order_by('id')

        return queryset

    @swagger_auto_schema(manual_parameters=[
        openapi.Parameter('search_param', openapi.IN_QUERY, description="", type=openapi.TYPE_STRING),
    ])
    def list(self, request, *args, **kwargs):
        queryset = self.search_param()

        paginator = CustomPaginationClassHotels()
        page = paginator.paginate_queryset(queryset, request)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            custom_data = {
                "message": "Hotels fetched successfully",
                "data": serializer.data,
                "status": status.HTTP_200_OK
            }
            return paginator.get_paginated_response(custom_data)

        serializer = self.get_serializer(queryset, many=True)
        custom_data = {
            "message": "Hotels fetched successfully",
            "data": serializer.data,
            "status": status.HTTP_200_OK
        }

        return Response(custom_data)

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            serializer.validated_data["is_active"] = False
            serializer.validated_data["is_user_registered"] = True
            serializer.save()

            email = serializer.validated_data["email_registration"]
            firstName = serializer.validated_data["first_name"]
            lastName = serializer.validated_data["last_name"]
            name = serializer.validated_data["name"]
            otaURL = serializer.validated_data["in_site_id"]

            # Send hotel registration email
            email_sent = send_hotel_registration_email(email, firstName, lastName, name, otaURL)

            if not email_sent:
                return Response({
                    'message': '施設の登録申請に失敗しました。',
                    'status': status.HTTP_400_BAD_REQUEST
                }, status=status.HTTP_400_BAD_REQUEST)

            return Response({
                "message": "施設の登録申請が提出されました。",
                "status": status.HTTP_201_CREATED
            }, status=status.HTTP_201_CREATED)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)

        if serializer.is_valid():
            serializer.validated_data["is_active"] = True
            serializer.save()
            return Response({
                'message': '施設の登録申請が更新されました。',
                "status": status.HTTP_200_OK
            }, status=status.HTTP_200_OK)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ApplyRegistrationHotelViewSet(viewsets.ModelViewSet):
    serializer_class = ApplyRegistrationHotelSerializer
    queryset = Hotels.objects.filter(is_user_registered=True)
    permission_classes = [IsAdminUser, IsTokenValid, IsAuthenticated]

    def list(self, request, *args, **kwargs):
        queryset = Hotels.objects.filter(is_user_registered=True)
        paginator = CustomPaginationClassHotels()
        page = paginator.paginate_queryset(queryset, request)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            custom_data = {
                "message": "Hotels fetched successfully",
                "data": serializer.data,
                "status": status.HTTP_200_OK,
                "total_pages": paginator.page.paginator.num_pages,
            }
            return paginator.get_paginated_response(custom_data)

        serializer = self.get_serializer(queryset, many=True)
        custom_data = {
            "message": "Hotels fetched successfully",
            "data": serializer.data,
            "status": status.HTTP_200_OK,
            "total_pages": paginator.page.paginator.num_pages,
        }


class CustomPaginationCompetitionRegistrations(PageNumberPagination):
    page_size = 15
    page_size_query_param = 'page_size'

    def get_paginated_response(self, data):
        return Response({
            'next': self.get_next_link(),
            'previous': self.get_previous_link(),
            'count': self.page.paginator.count if self.page else 0,
            'total_pages': self.page.paginator.num_pages if self.page else 0,
            'results': data
        })

class CompetitionRegistrationViewSet(viewsets.ModelViewSet):
    queryset = CompetitionRegistration.objects.all()
    serializer_class = CompetitionRegistrationSerializer
    permission_classes = [IsTokenValid, IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        hotel_id = self.request.query_params.get('hotel_id')

        if hotel_id:
            return CompetitionRegistration.objects.filter(hotel_id=hotel_id).order_by('-created_at')

        if not isinstance(user, AnonymousUser):
            return CompetitionRegistration.objects.filter(user=user).order_by('-created_at')
        return CompetitionRegistration.objects.none()

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        paginator = CustomPaginationCompetitionRegistrations()
        page = paginator.paginate_queryset(queryset, request)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            custom_data = {
                "message": "fetch data hotel competititon successfully",
                "data": serializer.data,
                "status": status.HTTP_200_OK
            }
            return paginator.get_paginated_response(custom_data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            try:
                serializer.save(user=request.user)
            except IntegrityError as e:
                return Response({
                    "message": "Record already exists.",
                    "status": status.HTTP_409_CONFLICT
                }, status=status.HTTP_409_CONFLICT)
            return Response({
                "message": "Successful competition registration.",
                "status": status.HTTP_201_CREATED
            }, status=status.HTTP_201_CREATED)
        else:
            return Response({
                "message": "Fail competition registration.",
                "status": status.HTTP_400_BAD_REQUEST
            }, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            self.perform_destroy(instance)
            return Response({
                "message": "Delete successfully.",
                "status": status.HTTP_204_NO_CONTENT
            }, status=status.HTTP_204_NO_CONTENT)
        except Exception as e:
            return Response({
                "message": "削除に失敗しました。",
                "status": status.HTTP_400_BAD_REQUEST
            }, status=status.HTTP_400_BAD_REQUEST)
