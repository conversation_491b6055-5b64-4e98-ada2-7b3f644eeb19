#!/bin/bash

# Script to check and activate backend services
# Usage: ./check_and_activate_backend.sh [local|server]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
LOCAL_BACKEND_DIR="./levitt_lite_be"
SERVER_BACKEND_DIR="/var/www/micado-web-api"
SERVER_HOST="micado.be"

# Default mode
MODE=${1:-"local"}

echo -e "${BLUE}=== Backend Service Checker & Activator ===${NC}"
echo -e "${BLUE}Mode: ${MODE}${NC}"
echo ""

# Function to print status
print_status() {
    local service=$1
    local status=$2
    if [ "$status" = "OK" ]; then
        echo -e "  ${GREEN}✓${NC} $service: ${GRE<PERSON>}$status${NC}"
    elif [ "$status" = "FAIL" ]; then
        echo -e "  ${RED}✗${NC} $service: ${RED}$status${NC}"
    else
        echo -e "  ${YELLOW}⚠${NC} $service: ${YELLOW}$status${NC}"
    fi
}

# Function to check and start service
check_and_start() {
    local service_name=$1
    local check_command=$2
    local start_command=$3
    local description=$4
    
    echo -e "${BLUE}Checking $description...${NC}"
    
    if eval "$check_command" >/dev/null 2>&1; then
        print_status "$service_name" "OK"
        return 0
    else
        print_status "$service_name" "FAIL"
        echo -e "  ${YELLOW}Starting $service_name...${NC}"
        
        if eval "$start_command"; then
            sleep 2
            if eval "$check_command" >/dev/null 2>&1; then
                print_status "$service_name" "STARTED"
                return 0
            else
                print_status "$service_name" "START_FAILED"
                return 1
            fi
        else
            print_status "$service_name" "START_FAILED"
            return 1
        fi
    fi
}

# Function for local environment
check_local_services() {
    echo -e "${BLUE}=== Checking Local Services ===${NC}"
    
    # Check MySQL
    check_and_start "MySQL" \
        "mysql -u root -e 'SELECT 1' 2>/dev/null" \
        "brew services start mysql" \
        "MySQL Database"
    
    # Check Redis
    check_and_start "Redis" \
        "redis-cli ping | grep -q PONG" \
        "brew services start redis" \
        "Redis Server"
    
    # Check Python Virtual Environment
    echo -e "${BLUE}Checking Python Virtual Environment...${NC}"
    if [ -d "$LOCAL_BACKEND_DIR/venv" ]; then
        print_status "Virtual Environment" "OK"
    else
        print_status "Virtual Environment" "FAIL"
        echo -e "  ${YELLOW}Creating virtual environment...${NC}"
        cd "$LOCAL_BACKEND_DIR"
        python3 -m venv venv
        source venv/bin/activate
        pip install -r requirements.txt
        cd ..
        print_status "Virtual Environment" "CREATED"
    fi
    
    # Check Django
    echo -e "${BLUE}Checking Django Application...${NC}"
    cd "$LOCAL_BACKEND_DIR"
    source venv/bin/activate
    
    if python manage.py check >/dev/null 2>&1; then
        print_status "Django" "OK"
    else
        print_status "Django" "FAIL"
        echo -e "  ${YELLOW}Running migrations...${NC}"
        python manage.py migrate
        print_status "Django" "MIGRATED"
    fi
    cd ..
    
    # Check if Django server is running
    echo -e "${BLUE}Checking Django Server...${NC}"
    if curl -s http://127.0.0.1:8000/api/ >/dev/null 2>&1; then
        print_status "Django Server" "RUNNING"
    else
        print_status "Django Server" "NOT_RUNNING"
        echo -e "  ${YELLOW}Starting Django server...${NC}"
        cd "$LOCAL_BACKEND_DIR"
        source venv/bin/activate
        nohup python manage.py runserver 127.0.0.1:8000 > django.log 2>&1 &
        echo $! > django.pid
        cd ..
        sleep 3
        if curl -s http://127.0.0.1:8000/api/ >/dev/null 2>&1; then
            print_status "Django Server" "STARTED"
        else
            print_status "Django Server" "START_FAILED"
        fi
    fi
    
    # Check Celery Worker
    echo -e "${BLUE}Checking Celery Worker...${NC}"
    if pgrep -f "celery.*worker" >/dev/null; then
        print_status "Celery Worker" "RUNNING"
    else
        print_status "Celery Worker" "NOT_RUNNING"
        echo -e "  ${YELLOW}Starting Celery worker...${NC}"
        cd "$LOCAL_BACKEND_DIR"
        source venv/bin/activate
        nohup celery -A main worker --loglevel=info > celery.log 2>&1 &
        echo $! > celery.pid
        cd ..
        sleep 3
        if pgrep -f "celery.*worker" >/dev/null; then
            print_status "Celery Worker" "STARTED"
        else
            print_status "Celery Worker" "START_FAILED"
        fi
    fi
}

# Function for server environment
check_server_services() {
    echo -e "${BLUE}=== Checking Server Services ===${NC}"
    
    # Check if we can connect to server
    echo -e "${BLUE}Checking Server Connection...${NC}"
    if ssh -o ConnectTimeout=5 "$SERVER_HOST" "echo 'Connected'" >/dev/null 2>&1; then
        print_status "Server Connection" "OK"
    else
        print_status "Server Connection" "FAIL"
        echo -e "${RED}Cannot connect to server. Please check SSH configuration.${NC}"
        exit 1
    fi
    
    # Check MySQL on server
    echo -e "${BLUE}Checking MySQL on Server...${NC}"
    if ssh "$SERVER_HOST" "mysql -u root -e 'SELECT 1' 2>/dev/null" >/dev/null 2>&1; then
        print_status "MySQL" "OK"
    else
        print_status "MySQL" "CHECK_MANUALLY"
    fi
    
    # Check Redis on server
    check_and_start "Redis" \
        "ssh $SERVER_HOST 'redis-cli ping | grep -q PONG'" \
        "ssh $SERVER_HOST 'sudo systemctl start redis'" \
        "Redis Server"
    
    # Check Nginx
    check_and_start "Nginx" \
        "ssh $SERVER_HOST 'sudo systemctl is-active nginx | grep -q active'" \
        "ssh $SERVER_HOST 'sudo systemctl start nginx'" \
        "Nginx Web Server"
    
    # Check uWSGI
    echo -e "${BLUE}Checking uWSGI...${NC}"
    if ssh "$SERVER_HOST" "ps aux | grep -v grep | grep uwsgi" >/dev/null 2>&1; then
        print_status "uWSGI" "RUNNING"
    else
        print_status "uWSGI" "NOT_RUNNING"
        echo -e "  ${YELLOW}Starting uWSGI...${NC}"
        ssh "$SERVER_HOST" "cd $SERVER_BACKEND_DIR && uwsgi --ini uwsgi.ini --daemonize uwsgi.log"
        sleep 3
        if ssh "$SERVER_HOST" "ps aux | grep -v grep | grep uwsgi" >/dev/null 2>&1; then
            print_status "uWSGI" "STARTED"
        else
            print_status "uWSGI" "START_FAILED"
        fi
    fi
    
    # Check Celery Worker on server
    echo -e "${BLUE}Checking Celery Worker on Server...${NC}"
    if ssh "$SERVER_HOST" "ps aux | grep -v grep | grep 'celery.*worker'" >/dev/null 2>&1; then
        print_status "Celery Worker" "RUNNING"
    else
        print_status "Celery Worker" "NOT_RUNNING"
        echo -e "  ${YELLOW}Starting Celery worker...${NC}"
        ssh "$SERVER_HOST" "cd $SERVER_BACKEND_DIR && nohup venv/bin/celery -A main worker --loglevel=info > celery.log 2>&1 &"
        sleep 3
        if ssh "$SERVER_HOST" "ps aux | grep -v grep | grep 'celery.*worker'" >/dev/null 2>&1; then
            print_status "Celery Worker" "STARTED"
        else
            print_status "Celery Worker" "START_FAILED"
        fi
    fi
    
    # Check API endpoints
    echo -e "${BLUE}Checking API Endpoints...${NC}"
    if curl -s https://api.levitt.ai/api/ >/dev/null 2>&1; then
        print_status "API Endpoint" "OK"
    else
        print_status "API Endpoint" "FAIL"
    fi
}

# Function to show service status summary
show_summary() {
    echo ""
    echo -e "${BLUE}=== Service Status Summary ===${NC}"
    
    if [ "$MODE" = "local" ]; then
        echo -e "${BLUE}Local Services:${NC}"
        echo "  - Django: http://127.0.0.1:8000"
        echo "  - API: http://127.0.0.1:8000/api/"
        echo "  - Admin: http://127.0.0.1:8000/admin/"
    else
        echo -e "${BLUE}Server Services:${NC}"
        echo "  - API: https://api.levitt.ai/api/"
        echo "  - Frontend: https://lite.levitt.ai"
    fi
    
    echo ""
    echo -e "${BLUE}Useful Commands:${NC}"
    if [ "$MODE" = "local" ]; then
        echo "  - Stop Django: kill \$(cat $LOCAL_BACKEND_DIR/django.pid)"
        echo "  - Stop Celery: kill \$(cat $LOCAL_BACKEND_DIR/celery.pid)"
        echo "  - View Django logs: tail -f $LOCAL_BACKEND_DIR/django.log"
        echo "  - View Celery logs: tail -f $LOCAL_BACKEND_DIR/celery.log"
    else
        echo "  - Check server logs: ssh $SERVER_HOST 'tail -f $SERVER_BACKEND_DIR/uwsgi.log'"
        echo "  - Check Celery logs: ssh $SERVER_HOST 'tail -f $SERVER_BACKEND_DIR/celery.log'"
        echo "  - Restart uWSGI: ssh $SERVER_HOST 'cd $SERVER_BACKEND_DIR && uwsgi --reload uwsgi.pid'"
    fi
}

# Main execution
case "$MODE" in
    "local")
        check_local_services
        ;;
    "server")
        check_server_services
        ;;
    *)
        echo -e "${RED}Invalid mode. Use 'local' or 'server'${NC}"
        echo "Usage: $0 [local|server]"
        exit 1
        ;;
esac

show_summary

echo ""
echo -e "${GREEN}=== Script completed ===${NC}"
