#!/bin/bash

# Advanced Server Health Check and Recovery Script
# Usage: ./server_health_check.sh [check|restart|logs|full]

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Configuration
SERVER_HOST="micado.be"
BACKEND_DIR="/var/www/micado-web-api"
API_URL="https://api.levitt.ai"
FRONTEND_URL="https://lite.levitt.ai"

# Default action
ACTION=${1:-"check"}

# Function to print status with timestamp
print_status() {
    local service=$1
    local status=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    if [ "$status" = "OK" ]; then
        echo -e "[$timestamp] ${GREEN}✓${NC} $service: ${GREEN}$status${NC}"
    elif [ "$status" = "FAIL" ]; then
        echo -e "[$timestamp] ${RED}✗${NC} $service: ${RED}$status${NC}"
    elif [ "$status" = "WARN" ]; then
        echo -e "[$timestamp] ${YELLOW}⚠${NC} $service: ${YELLOW}$status${NC}"
    else
        echo -e "[$timestamp] ${BLUE}ℹ${NC} $service: ${BLUE}$status${NC}"
    fi
}

# Function to execute command on server
server_exec() {
    ssh "$SERVER_HOST" "$1" 2>/dev/null
}

# Function to check service health
check_service_health() {
    echo -e "${PURPLE}=== Server Health Check ===${NC}"
    echo ""
    
    # 1. Server Connection
    echo -e "${BLUE}1. Checking Server Connection...${NC}"
    if ssh -o ConnectTimeout=5 "$SERVER_HOST" "echo 'Connected'" >/dev/null 2>&1; then
        print_status "Server SSH" "OK"
    else
        print_status "Server SSH" "FAIL"
        return 1
    fi
    
    # 2. System Resources
    echo -e "${BLUE}2. Checking System Resources...${NC}"
    local cpu_usage=$(server_exec "top -bn1 | grep 'Cpu(s)' | awk '{print \$2}' | cut -d'%' -f1")
    local memory_usage=$(server_exec "free | grep Mem | awk '{printf \"%.1f\", \$3/\$2 * 100.0}'")
    local disk_usage=$(server_exec "df -h / | awk 'NR==2{print \$5}' | cut -d'%' -f1")
    
    print_status "CPU Usage" "${cpu_usage}%"
    print_status "Memory Usage" "${memory_usage}%"
    print_status "Disk Usage" "${disk_usage}%"
    
    # 3. Database
    echo -e "${BLUE}3. Checking Database...${NC}"
    if server_exec "mysql -u root -e 'SELECT 1' >/dev/null 2>&1"; then
        print_status "MySQL" "OK"
        local db_size=$(server_exec "mysql -u root -e \"SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'DB Size (MB)' FROM information_schema.tables WHERE table_schema='micado_web_dev';\" | tail -1")
        print_status "Database Size" "${db_size} MB"
    else
        print_status "MySQL" "FAIL"
    fi
    
    # 4. Redis
    echo -e "${BLUE}4. Checking Redis...${NC}"
    if server_exec "redis-cli ping | grep -q PONG"; then
        print_status "Redis" "OK"
        local redis_memory=$(server_exec "redis-cli info memory | grep used_memory_human | cut -d: -f2 | tr -d '\r'")
        print_status "Redis Memory" "$redis_memory"
    else
        print_status "Redis" "FAIL"
    fi
    
    # 5. Nginx
    echo -e "${BLUE}5. Checking Nginx...${NC}"
    if server_exec "sudo systemctl is-active nginx | grep -q active"; then
        print_status "Nginx" "OK"
        local nginx_connections=$(server_exec "ss -tuln | grep :80 | wc -l")
        print_status "Nginx Connections" "$nginx_connections"
    else
        print_status "Nginx" "FAIL"
    fi
    
    # 6. uWSGI
    echo -e "${BLUE}6. Checking uWSGI...${NC}"
    local uwsgi_processes=$(server_exec "ps aux | grep -v grep | grep uwsgi | wc -l")
    if [ "$uwsgi_processes" -gt 0 ]; then
        print_status "uWSGI" "OK ($uwsgi_processes processes)"
    else
        print_status "uWSGI" "FAIL"
    fi
    
    # 7. Celery Worker
    echo -e "${BLUE}7. Checking Celery Worker...${NC}"
    local celery_processes=$(server_exec "ps aux | grep -v grep | grep 'celery.*worker' | wc -l")
    if [ "$celery_processes" -gt 0 ]; then
        print_status "Celery Worker" "OK ($celery_processes processes)"
    else
        print_status "Celery Worker" "FAIL"
    fi
    
    # 8. API Endpoints
    echo -e "${BLUE}8. Checking API Endpoints...${NC}"
    local api_response=$(curl -s -o /dev/null -w "%{http_code}" "$API_URL/api/" || echo "000")
    if [ "$api_response" = "200" ]; then
        print_status "API Root" "OK (HTTP $api_response)"
    else
        print_status "API Root" "FAIL (HTTP $api_response)"
    fi
    
    local gpt_response=$(curl -s -o /dev/null -w "%{http_code}" "$API_URL/api/gpt_plans/create/asy/" || echo "000")
    if [ "$gpt_response" = "401" ]; then
        print_status "GPT Plans API" "OK (HTTP $gpt_response - Auth Required)"
    else
        print_status "GPT Plans API" "WARN (HTTP $gpt_response)"
    fi
    
    # 9. Frontend
    echo -e "${BLUE}9. Checking Frontend...${NC}"
    local frontend_response=$(curl -s -o /dev/null -w "%{http_code}" "$FRONTEND_URL" || echo "000")
    if [ "$frontend_response" = "200" ]; then
        print_status "Frontend" "OK (HTTP $frontend_response)"
    else
        print_status "Frontend" "FAIL (HTTP $frontend_response)"
    fi
    
    # 10. SSL Certificate
    echo -e "${BLUE}10. Checking SSL Certificate...${NC}"
    local ssl_expiry=$(echo | openssl s_client -servername api.levitt.ai -connect api.levitt.ai:443 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2)
    if [ -n "$ssl_expiry" ]; then
        print_status "SSL Certificate" "OK (Expires: $ssl_expiry)"
    else
        print_status "SSL Certificate" "WARN"
    fi
}

# Function to restart services
restart_services() {
    echo -e "${PURPLE}=== Restarting Services ===${NC}"
    echo ""
    
    # Restart uWSGI
    echo -e "${BLUE}Restarting uWSGI...${NC}"
    server_exec "sudo pkill -f uwsgi"
    sleep 2
    server_exec "cd $BACKEND_DIR && uwsgi --ini uwsgi.ini --daemonize uwsgi.log"
    sleep 3
    if server_exec "ps aux | grep -v grep | grep uwsgi" >/dev/null; then
        print_status "uWSGI Restart" "OK"
    else
        print_status "uWSGI Restart" "FAIL"
    fi
    
    # Restart Celery
    echo -e "${BLUE}Restarting Celery Worker...${NC}"

    # Kill existing celery processes (non-blocking)
    server_exec "pkill -f 'celery.*worker' || true" &
    sleep 2

    # Start new celery worker (non-blocking)
    server_exec "cd $BACKEND_DIR && nohup venv/bin/celery -A main worker --loglevel=info > celery.log 2>&1 &" &
    sleep 3

    # Check if started successfully
    local celery_count=$(server_exec "ps aux | grep -v grep | grep 'celery.*worker' | wc -l" 2>/dev/null || echo "0")
    if [ "$celery_count" -gt 0 ]; then
        print_status "Celery Restart" "OK ($celery_count processes)"
    else
        print_status "Celery Restart" "FAIL"
    fi
    
    # Reload Nginx
    echo -e "${BLUE}Reloading Nginx...${NC}"
    if server_exec "sudo nginx -t && sudo systemctl reload nginx"; then
        print_status "Nginx Reload" "OK"
    else
        print_status "Nginx Reload" "FAIL"
    fi
}

# Function to show logs
show_logs() {
    echo -e "${PURPLE}=== Recent Logs ===${NC}"
    echo ""
    
    echo -e "${BLUE}uWSGI Logs (last 20 lines):${NC}"
    server_exec "tail -20 $BACKEND_DIR/uwsgi.log"
    echo ""
    
    echo -e "${BLUE}Celery Logs (last 20 lines):${NC}"
    server_exec "tail -20 $BACKEND_DIR/celery.log"
    echo ""
    
    echo -e "${BLUE}Nginx Error Logs (last 10 lines):${NC}"
    server_exec "sudo tail -10 /var/log/nginx/error.log"
    echo ""
}

# Function for full health check and auto-recovery
full_check_and_recovery() {
    echo -e "${PURPLE}=== Full Health Check & Auto Recovery ===${NC}"
    echo ""
    
    check_service_health
    
    echo ""
    echo -e "${BLUE}=== Auto Recovery ===${NC}"
    
    # Check if critical services are down and restart them
    local needs_restart=false
    
    if ! server_exec "ps aux | grep -v grep | grep uwsgi" >/dev/null; then
        echo -e "${YELLOW}uWSGI is down, will restart...${NC}"
        needs_restart=true
    fi
    
    if ! server_exec "ps aux | grep -v grep | grep 'celery.*worker'" >/dev/null; then
        echo -e "${YELLOW}Celery is down, will restart...${NC}"
        needs_restart=true
    fi
    
    if [ "$needs_restart" = true ]; then
        restart_services
        echo ""
        echo -e "${BLUE}Re-checking after restart...${NC}"
        sleep 5
        check_service_health
    else
        echo -e "${GREEN}All critical services are running.${NC}"
    fi
}

# Main execution
case "$ACTION" in
    "check")
        check_service_health
        ;;
    "restart")
        restart_services
        ;;
    "logs")
        show_logs
        ;;
    "full")
        full_check_and_recovery
        ;;
    *)
        echo -e "${RED}Invalid action. Use: check, restart, logs, or full${NC}"
        echo "Usage: $0 [check|restart|logs|full]"
        echo ""
        echo "Actions:"
        echo "  check   - Check all services status"
        echo "  restart - Restart uWSGI and Celery"
        echo "  logs    - Show recent logs"
        echo "  full    - Full check with auto-recovery"
        exit 1
        ;;
esac

echo ""
echo -e "${GREEN}=== Script completed ===${NC}"
