---
description: 
globs: micado-web-fe/
alwaysApply: false
---
# Frontend Development Rules - ReactJS + JavaScript

## Auto-attach Configuration
**Target Directory**: `micado-web-fe/`
**Apply to**: All ReactJS frontend development in the micado-web-fe directory

## Technology Stack
- **Framework**: React 18.2.0
- **Language**: JavaScript (ES6+)
- **State Management**: Redux Toolkit
- **UI Library**: Material-UI (MUI) 5.14.4
- **Styling**: SASS/SCSS
- **Form Handling**: Formik + Yup
- **HTTP Client**: Axios
- **Routing**: React Router DOM 5.3.4
- **Charts**: Chart.js 2, Recharts
- **Date Handling**: Date-fns, Moment.js, Day.js
- **Testing**: Jest, React Testing Library

## Project Structure
```
micado-web-fe/
├── public/                  # Static files
├── src/
│   ├── components/          # Reusable components
│   ├── pages/               # Page components
│   ├── features/            # Redux slices and API logic
│   ├── assets/              # Images, fonts, icons
│   ├── constants/           # Constants and configuration
│   ├── routes/              # Route definitions
│   ├── App.js               # Main app component
│   ├── App.scss             # Global styles
│   ├── index.js             # Entry point
│   ├── store.js             # Redux store configuration
│   └── helper.js            # Utility functions
├── package.json             # Dependencies and scripts
└── README.md               # Documentation
```

## Development Guidelines

### 1. Code Style & Standards
- Follow JavaScript ES6+ standards
- Use consistent naming conventions:
  - **Components**: PascalCase (e.g., `UserProfile`)
  - **Files**: camelCase for JS, PascalCase for components
  - **Variables/Functions**: camelCase
  - **Constants**: UPPER_SNAKE_CASE
- Use English for all code comments and documentation
- Maximum line length: 120 characters
- Use semicolons consistently
- Prefer arrow functions for consistency

### 2. Component Structure
```javascript
// Functional component template
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import styles from './ComponentName.module.scss';

const ComponentName = ({ prop1, prop2, onAction }) => {
  const [state, setState] = useState(null);

  useEffect(() => {
    // Effect logic here
  }, []);

  const handleAction = () => {
    // Handler logic
    onAction && onAction();
  };

  return (
    <div className={styles.container}>
      {/* Component JSX */}
    </div>
  );
};

ComponentName.propTypes = {
  prop1: PropTypes.string.isRequired,
  prop2: PropTypes.number,
  onAction: PropTypes.func
};

ComponentName.defaultProps = {
  prop2: 0,
  onAction: null
};

export default ComponentName;
```

### 3. File Organization
- **Components**: Reusable UI components
  - Create folder for each component with `index.js` and `styles.module.scss`
  - Export component as default from `index.js`
- **Pages**: Route-level components
  - Organize by feature/route
  - Include page-specific styles
- **Features**: Redux slices and API logic
  - One slice per feature
  - Include actions, reducers, and selectors
- **Assets**: Static files (images, fonts, icons)
- **Constants**: Configuration and constant values

### 4. State Management (Redux Toolkit)
```javascript
// Redux slice example
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

// Async thunk for API calls
export const fetchUsers = createAsyncThunk(
  'users/fetchUsers',
  async (params, { rejectWithValue }) => {
    try {
      const response = await api.getUsers(params);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

const usersSlice = createSlice({
  name: 'users',
  initialState: {
    data: [],
    loading: false,
    error: null
  },
  reducers: {
    clearUsers: (state) => {
      state.data = [];
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchUsers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUsers.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload;
      })
      .addCase(fetchUsers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  }
});

export const { clearUsers } = usersSlice.actions;
export default usersSlice.reducer;
```

### 5. API Integration (Axios)
```javascript
// API service example
import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Request interceptor for authentication
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default apiClient;
```

### 6. Form Handling (Formik + Yup)
```javascript
import { Formik, Form, Field } from 'formik';
import * as Yup from 'yup';

const validationSchema = Yup.object({
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),
  password: Yup.string()
    .min(8, 'Password must be at least 8 characters')
    .required('Password is required')
});

const LoginForm = ({ onSubmit }) => {
  return (
    <Formik
      initialValues={{ email: '', password: '' }}
      validationSchema={validationSchema}
      onSubmit={onSubmit}
    >
      {({ errors, touched, isSubmitting }) => (
        <Form>
          <Field name="email" type="email" />
          {errors.email && touched.email && <div>{errors.email}</div>}
          
          <Field name="password" type="password" />
          {errors.password && touched.password && <div>{errors.password}</div>}
          
          <button type="submit" disabled={isSubmitting}>
            Submit
          </button>
        </Form>
      )}
    </Formik>
  );
};
```

### 7. Styling Guidelines (SASS/SCSS)
- Use SCSS modules for component-specific styles
- Follow BEM methodology for class naming
- Use Material-UI theme system
- Create consistent spacing and color variables
- Mobile-first responsive design

```scss
// Component styles example
.container {
  padding: 1rem;
  background-color: var(--background-color);
  
  &__header {
    margin-bottom: 1rem;
    font-size: 1.5rem;
    font-weight: 600;
  }
  
  &__content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    
    @media (min-width: 768px) {
      flex-direction: row;
    }
  }
  
  &--loading {
    opacity: 0.7;
    pointer-events: none;
  }
}
```

### 8. Material-UI Integration
```javascript
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { Button, TextField, Box } from '@mui/material';

const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2'
    },
    secondary: {
      main: '#dc004e'
    }
  }
});

const App = () => {
  return (
    <ThemeProvider theme={theme}>
      <Box sx={{ padding: 2 }}>
        <TextField
          label="Email"
          variant="outlined"
          fullWidth
          margin="normal"
        />
        <Button
          variant="contained"
          color="primary"
          fullWidth
        >
          Submit
        </Button>
      </Box>
    </ThemeProvider>
  );
};
```

### 9. Routing (React Router DOM)
```javascript
import { BrowserRouter as Router, Route, Switch } from 'react-router-dom';
import PrivateRoute from './PrivateRoute';
import PublicRoute from './PublicRoute';

const AppRoutes = () => {
  return (
    <Router>
      <Switch>
        <PublicRoute exact path="/login" component={LoginPage} />
        <PublicRoute exact path="/register" component={RegisterPage} />
        <PrivateRoute exact path="/dashboard" component={DashboardPage} />
        <PrivateRoute exact path="/profile" component={ProfilePage} />
        <Route path="*" component={NotFoundPage} />
      </Switch>
    </Router>
  );
};
```

### 10. Error Handling
```javascript
import { toast } from 'react-toastify';

// Error boundary component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <h1>Something went wrong.</h1>;
    }

    return this.props.children;
  }
}

// Toast notifications for user feedback
const showSuccessToast = (message) => {
  toast.success(message, {
    position: 'top-right',
    autoClose: 3000
  });
};

const showErrorToast = (message) => {
  toast.error(message, {
    position: 'top-right',
    autoClose: 5000
  });
};
```

### 11. Testing Guidelines
```javascript
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { store } from '../store';
import ComponentName from './ComponentName';

// Test component with Redux provider
const renderWithProvider = (component) => {
  return render(
    <Provider store={store}>
      {component}
    </Provider>
  );
};

describe('ComponentName', () => {
  test('renders component correctly', () => {
    renderWithProvider(<ComponentName />);
    expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });

  test('handles user interaction', async () => {
    renderWithProvider(<ComponentName />);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    await waitFor(() => {
      expect(screen.getByText('Updated Text')).toBeInTheDocument();
    });
  });
});
```

### 12. Performance Optimization
- Use React.memo for preventing unnecessary re-renders
- Implement lazy loading for routes and components
- Optimize images and assets
- Use React DevTools for performance profiling
- Implement virtual scrolling for large lists

```javascript
import React, { memo, lazy, Suspense } from 'react';

// Lazy loading
const LazyComponent = lazy(() => import('./LazyComponent'));

// Memoized component
const OptimizedComponent = memo(({ data }) => {
  return (
    <div>
      {data.map(item => (
        <div key={item.id}>{item.name}</div>
      ))}
    </div>
  );
});

// Usage with Suspense
const App = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <LazyComponent />
    </Suspense>
  );
};
```

### 13. Environment Configuration
```javascript
// Environment variables (in .env file)
REACT_APP_API_URL=http://localhost:8000
REACT_APP_ENV=development
REACT_APP_VERSION=1.0.0

// Usage in code
const config = {
  apiUrl: process.env.REACT_APP_API_URL,
  environment: process.env.REACT_APP_ENV,
  version: process.env.REACT_APP_VERSION
};
```

### 14. Build & Deployment
- Use `npm run build` for production builds
- Optimize bundle size with code splitting
- Configure proper caching headers
- Use environment-specific configurations
- Implement proper CI/CD pipeline

## Common Commands
```bash
# Install dependencies
npm install

# Start development server
npm start

# Run tests
npm test

# Build for production
npm run build

# Analyze bundle size
npm run build -- --analyze

# Lint code
npm run lint

# Format code
npm run format
```

## Best Practices
1. **Component Design**: Keep components small and focused
2. **State Management**: Use local state when possible, Redux for global state
3. **Performance**: Optimize re-renders and bundle size
4. **Accessibility**: Follow WCAG guidelines
5. **Security**: Sanitize user inputs and validate data
6. **Testing**: Write comprehensive tests for critical functionality
7. **Documentation**: Document complex logic and component APIs
8. **Version Control**: Use meaningful commit messages and branch names

## Code Quality Tools
- ESLint for code linting
- Prettier for code formatting
- Husky for pre-commit hooks
- React DevTools for debugging
- Bundle analyzer for optimization
