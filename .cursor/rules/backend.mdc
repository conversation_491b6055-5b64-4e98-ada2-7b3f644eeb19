---
description: 
globs: micado-web/
alwaysApply: false
---
# Backend Development Rules - Django + MySQL

## Auto-attach Configuration
**Target Directory**: `micado-web/`
**Apply to**: All Django backend development in the micado-web directory

## Quick Start Guide
```bash
# 1. Navigate to backend directory
cd micado-web/

# 2. ALWAYS activate virtual environment first
source venv/bin/activate

# 3. Install dependencies
pip install -r requirements.txt

# 4. Apply migrations
python manage.py migrate

# 5. Start development server
python manage.py runserver

# 6. In new terminal: activate venv and start Celery
source venv/bin/activate
celery -A main worker -l info
```

**⚠️ IMPORTANT**: Never run Django commands without activating the virtual environment!

## Technology Stack
- **Framework**: Django 4.2.4
- **Database**: MySQL
- **API**: Django REST Framework
- **Authentication**: JWT (Simple JWT)
- **Task Queue**: Celery + Redis
- **Documentation**: drf-yasg (Swagger)

## Project Structure
```
micado-web/
├── main/                    # Django project settings
├── users/                   # User management app
├── hotels/                  # Hotel management app
├── plans/                   # Plan management app
├── group_plans/             # Group plan management app
├── gpt_plans/               # AI-generated plans app
├── notifications/           # Notification system app
├── otas/                    # OTA management app
├── reports/                 # Reporting app
├── contact/                 # Contact management app
├── static/                  # Static files
├── media/                   # Media files
├── requirements.txt         # Python dependencies
└── manage.py               # Django management script
```

## Development Guidelines

### 1. Code Style & Standards
- Follow PEP 8 for Python code formatting
- Use English for all code comments and docstrings
- Use meaningful variable and function names
- Maximum line length: 120 characters
- Use type hints where appropriate

### 2. Django App Structure
Each Django app should follow this structure:
```
app_name/
├── __init__.py
├── admin.py                 # Admin interface configuration
├── apps.py                  # App configuration
├── models.py                # Database models
├── views.py                 # API views
├── serializers.py           # DRF serializers
├── urls.py                  # URL routing (if needed)
├── tests.py                 # Unit tests
├── migrations/              # Database migrations
└── management/              # Custom management commands (if needed)
```

### 3. Database Guidelines
- Use MySQL as primary database
- Always create migrations for model changes: `python manage.py makemigrations`
- Apply migrations: `python manage.py migrate`
- Use descriptive field names and add help_text for documentation
- Add proper indexes for frequently queried fields
- Use foreign keys and relationships appropriately

### 4. Models Best Practices
```python
from django.db import models
from django.contrib.auth.models import User

class BaseModel(models.Model):
    """Base model with common fields"""
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        abstract = True

class ExampleModel(BaseModel):
    """Example model with proper documentation"""
    name = models.CharField(max_length=255, help_text="Name of the item")
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'example_model'
        verbose_name = 'Example Model'
        verbose_name_plural = 'Example Models'
    
    def __str__(self):
        return self.name
```

### 5. API Development (Django REST Framework)
- Use ViewSets for CRUD operations
- Implement proper serializers for data validation
- Use pagination for list endpoints
- Implement proper error handling
- Add API documentation with drf-yasg

```python
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

class ExampleViewSet(viewsets.ModelViewSet):
    """Example ViewSet with proper structure"""
    queryset = ExampleModel.objects.all()
    serializer_class = ExampleSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """Filter queryset based on user permissions"""
        return self.queryset.filter(user=self.request.user)
    
    @action(detail=True, methods=['post'])
    def custom_action(self, request, pk=None):
        """Custom action example"""
        instance = self.get_object()
        # Custom logic here
        return Response({'status': 'success'})
```

### 6. Authentication & Security
- Use JWT tokens for API authentication
- Implement proper permission classes
- Validate all input data through serializers
- Use HTTPS in production
- Set proper CORS headers
- Keep SECRET_KEY secure using environment variables

### 7. Environment Configuration
- Use python-dotenv for environment variables
- Keep sensitive data in .env file (not in version control)
- Required environment variables:
  ```
  SECRET_KEY=your-secret-key
  DB_NAME=database-name
  DB_USER=database-user
  DB_PASSWORD=database-password
  DB_HOST=database-host
  DB_PORT=database-port
  EMAIL_HOST=smtp-host
  EMAIL_HOST_USER=email-user
  EMAIL_HOST_PASSWORD=email-password
  EMAIL_PORT=email-port
  EMAIL_USE_TLS=True/False
  ```

### 8. Testing Guidelines
- Write unit tests for all models and views
- Use Django's TestCase for database-related tests
- Mock external API calls
- Aim for >80% code coverage
- Run tests: `python manage.py test`

### 9. Database Migrations
- Always review migration files before applying
- Use descriptive migration names
- Test migrations on development data
- Create data migrations for complex data transformations
- Never edit applied migrations

### 10. Performance Optimization
- Use select_related() and prefetch_related() for database queries
- Implement database indexes for frequently queried fields
- Use Django's caching framework with Redis
- Optimize API responses with proper serializer fields
- Monitor database query performance

### 11. Error Handling
- Use Django's built-in exception handling
- Implement custom exception handlers for API errors
- Log errors appropriately
- Return meaningful error messages to frontend

### 12. Celery Tasks (Background Processing)
- Use Celery for long-running tasks
- Keep tasks idempotent
- Implement proper error handling in tasks
- Use Redis as message broker

### 13. File Organization
- Keep related functionality in the same app
- Use services.py for complex business logic
- Create utils.py for utility functions
- Organize imports: standard library, third-party, local imports

### 14. Documentation
- Document all API endpoints with drf-yasg
- Add docstrings to all classes and methods
- Keep README.md updated
- Document deployment procedures

### 15. Deployment
- Use environment-specific settings
- Set DEBUG=False in production
- Use proper logging configuration
- Implement health check endpoints
- Use uWSGI for production deployment

## Virtual Environment Setup
**IMPORTANT**: Always work within the virtual environment located in `micado-web/venv/`

### Activate Virtual Environment
```bash
# On macOS/Linux
source venv/bin/activate

# Verify activation (should show (venv) in terminal prompt)
which python  # Should point to venv/bin/python
```

### Deactivate Virtual Environment
```bash
deactivate
```

### Install Dependencies
```bash
# After activating venv
pip install -r requirements.txt

# To add new dependencies
pip install package_name
pip freeze > requirements.txt
```

## Common Commands
**Note**: All commands below must be run with activated virtual environment

```bash
# 1. ALWAYS activate virtual environment first
source venv/bin/activate

# 2. Install/update dependencies
pip install -r requirements.txt

# 3. Create new Django app
python manage.py startapp app_name

# 4. Create and apply migrations
python manage.py makemigrations
python manage.py migrate

# 5. Create superuser
python manage.py createsuperuser

# 6. Run development server
python manage.py runserver

# 7. Run tests
python manage.py test

# 8. Collect static files
python manage.py collectstatic

# 9. Start Celery worker
celery -A main worker -l info

# 10. Django shell
python manage.py shell

# 11. Database shell
python manage.py dbshell
```

## Daily Development Workflow
```bash
# 1. Navigate to project directory
cd micado-web/

# 2. Activate virtual environment
source venv/bin/activate

# 3. Install/update dependencies (if needed)
pip install -r requirements.txt

# 4. Apply any new migrations
python manage.py migrate

# 5. Start development server
python manage.py runserver

# 6. In another terminal (also activate venv first)
source venv/bin/activate
celery -A main worker -l info
```

## Dependencies Management
- Keep requirements.txt updated
- Use specific versions for production dependencies
- Regularly update dependencies for security patches
- Test thoroughly after dependency updates

## Virtual Environment Best Practices
### 1. Always Check Virtual Environment Status
```bash
# Check if venv is activated
echo $VIRTUAL_ENV  # Should show path to venv directory

# Check Python path
which python  # Should point to venv/bin/python
which pip     # Should point to venv/bin/pip
```

### 2. Troubleshooting Virtual Environment
```bash
# If venv is not working properly, recreate it
rm -rf venv/
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# If permission issues on macOS/Linux
chmod +x venv/bin/activate

# If Python version issues
python3 --version  # Check Python version
which python3      # Check Python location
```

### 3. IDE Configuration
- **VS Code**: Select Python interpreter from venv
  - `Cmd+Shift+P` → "Python: Select Interpreter"
  - Choose `./venv/bin/python`
- **PyCharm**: Configure project interpreter to use venv
- **Cursor**: Ensure terminal uses activated venv

### 4. Environment Variables
```bash
# Create .env file in micado-web/ directory
touch .env

# Add environment variables (never commit this file)
# Example .env content:
SECRET_KEY=your-secret-key-here
DB_NAME=micado_db
DB_USER=root
DB_PASSWORD=your-password
DB_HOST=localhost
DB_PORT=3306
DEBUG=True
```

### 5. Common Issues and Solutions
- **ModuleNotFoundError**: Ensure venv is activated and dependencies installed
- **Permission denied**: Check file permissions and venv activation
- **Wrong Python version**: Recreate venv with correct Python version
- **Import errors**: Verify PYTHONPATH and Django settings

## Git Workflow
- Use feature branches for development
- Write meaningful commit messages
- Review code before merging
- Keep main branch stable
- Tag releases properly
