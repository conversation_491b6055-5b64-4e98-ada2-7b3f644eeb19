---
description:
globs:
alwaysApply: true
---

# <PERSON>hi run server backend cho hệ thống
- cần khởi động redis server
- luôn đi đến và thực hiện trong thư mục `micado-web`
- luô<PERSON> luôn activate venv trước khi thực hiện các code python
- cần phải chạy install các package với pip trong @requirements.txt
- cần thực hiện migrate
- cần thực hiện chạy celery
- cuối cùng là runserver
- luôn luôn sử dụng rules: @backend.mdc

# Khi run server front cho hệ thống
- luôn đi đến và thực hiện trong thư mục `micado-web-fe`
- luôn install package với npm install
- cuối cùng là npm start
- luôn luôn sử dụng rules: @frontend.mdc