{"rules": {"backend": {"file": "backend.mdc", "directories": ["micado-web/"], "description": "Django backend development rules with MySQL", "autoAttach": true}, "frontend": {"file": "frontend.mdc", "directories": ["micado-web-fe/"], "description": "ReactJS frontend development rules with JavaScript", "autoAttach": true}}, "autoAttach": {"micado-web/": {"rules": ["backend"], "description": "Auto-attach backend rules when working in micado-web directory"}, "micado-web-fe/": {"rules": ["frontend"], "description": "Auto-attach frontend rules when working in micado-web-fe directory"}}}